const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"test.dashboard":{"uri":"test-dashboard","methods":["GET","HEAD"]},"test.simple":{"uri":"test-simple","methods":["GET","HEAD"]},"jobs.index":{"uri":"jobs","methods":["GET","HEAD"]},"freelancer.onboarding":{"uri":"onboarding\/freelancer","methods":["GET","HEAD"]},"client.onboarding":{"uri":"onboarding\/client","methods":["GET","HEAD"]},"client.onboarding.skip":{"uri":"onboarding\/client\/skip","methods":["POST"]},"profile.edit":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["PATCH"]},"profile.destroy":{"uri":"profile","methods":["DELETE"]},"jobs.create":{"uri":"jobs\/create","methods":["GET","HEAD"]},"jobs.store":{"uri":"jobs","methods":["POST"]},"jobs.edit":{"uri":"jobs\/{job}\/edit","methods":["GET","HEAD"],"parameters":["job"],"bindings":{"job":"id"}},"jobs.update":{"uri":"jobs\/{job}","methods":["PATCH"],"parameters":["job"],"bindings":{"job":"id"}},"jobs.destroy":{"uri":"jobs\/{job}","methods":["DELETE"],"parameters":["job"],"bindings":{"job":"id"}},"jobs.show":{"uri":"jobs\/{job}","methods":["GET","HEAD"],"parameters":["job"],"bindings":{"job":"id"}},"bids.index":{"uri":"bids","methods":["GET","HEAD"]},"bids.store":{"uri":"bids","methods":["POST"]},"messages.index":{"uri":"messages","methods":["GET","HEAD"]},"messages.users":{"uri":"messages\/users","methods":["GET","HEAD"]},"messages.conversation":{"uri":"messages\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"messages.store":{"uri":"messages","methods":["POST"]},"messages.unread.count":{"uri":"messages\/unread\/count","methods":["GET","HEAD"]},"messages.read":{"uri":"messages\/{message}\/read","methods":["PATCH"],"parameters":["message"],"bindings":{"message":"id"}},"reports.index":{"uri":"reports","methods":["GET","HEAD"]},"bids.show":{"uri":"bids\/{bid}","methods":["GET","HEAD"],"parameters":["bid"],"bindings":{"bid":"id"}},"bids.update":{"uri":"bids\/{bid}","methods":["PATCH"],"parameters":["bid"],"bindings":{"bid":"id"}},"bids.destroy":{"uri":"bids\/{bid}","methods":["DELETE"],"parameters":["bid"],"bindings":{"bid":"id"}},"bids.updateStatus":{"uri":"bids\/{bid}\/status","methods":["PATCH"],"parameters":["bid"]},"test.bid":{"uri":"test-bid\/{bid}","methods":["PATCH"],"parameters":["bid"]},"projects.index":{"uri":"projects","methods":["GET","HEAD"]},"projects.show":{"uri":"projects\/{project}","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"projects.complete":{"uri":"projects\/{project}\/complete","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"projects.approve":{"uri":"projects\/{project}\/approve","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"projects.requestRevision":{"uri":"projects\/{project}\/request-revision","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"projects.cancel":{"uri":"projects\/{project}\/cancel","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"projects.review":{"uri":"projects\/{project}\/review","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"contracts.index":{"uri":"contracts","methods":["GET","HEAD"]},"contracts.show":{"uri":"contracts\/{contract}","methods":["GET","HEAD"],"parameters":["contract"],"bindings":{"contract":"id"}},"contracts.sign":{"uri":"contracts\/{contract}\/sign","methods":["GET","HEAD"],"parameters":["contract"],"bindings":{"contract":"id"}},"contracts.processSignature":{"uri":"contracts\/{contract}\/signature","methods":["POST"],"parameters":["contract"],"bindings":{"contract":"id"}},"contracts.downloadPdf":{"uri":"contracts\/{contract}\/pdf","methods":["GET","HEAD"],"parameters":["contract"],"bindings":{"contract":"id"}},"contracts.cancel":{"uri":"contracts\/{contract}\/cancel","methods":["POST"],"parameters":["contract"],"bindings":{"contract":"id"}},"payment.show":{"uri":"projects\/{project}\/payment","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"payment.intent":{"uri":"projects\/{project}\/payment\/intent","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"payment.confirm":{"uri":"payment\/confirm","methods":["POST"]},"payment.release":{"uri":"projects\/{project}\/payment\/release","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"payment.refund":{"uri":"projects\/{project}\/payment\/refund","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"payment.history":{"uri":"payment\/history","methods":["GET","HEAD"]},"transactions.show":{"uri":"transactions\/{transaction}","methods":["GET","HEAD"],"parameters":["transaction"],"bindings":{"transaction":"id"}},"ai.recommendations":{"uri":"ai\/recommendations","methods":["GET","HEAD"]},"messages.download":{"uri":"messages\/{message}\/download","methods":["GET","HEAD"],"parameters":["message"],"bindings":{"message":"id"}},"analytics.index":{"uri":"analytics","methods":["GET","HEAD"]},"analytics.earnings":{"uri":"analytics\/earnings","methods":["GET","HEAD"]},"analytics.projects":{"uri":"analytics\/projects","methods":["GET","HEAD"]},"analytics.performance":{"uri":"analytics\/performance","methods":["GET","HEAD"]},"analytics.export":{"uri":"analytics\/export","methods":["GET","HEAD"]},"client.wallet":{"uri":"client\/wallet","methods":["GET","HEAD"]},"client.wallet.create-intent":{"uri":"client\/wallet\/create-intent","methods":["POST"]},"freelancer.wallet":{"uri":"freelancer\/wallet","methods":["GET","HEAD"]},"freelancer.wallet.withdraw":{"uri":"freelancer\/wallet\/withdraw","methods":["POST"]},"deposits.index":{"uri":"deposits","methods":["GET","HEAD"]},"stripe.webhook":{"uri":"stripe\/webhook","methods":["POST"]},"role.selection":{"uri":"join","methods":["GET","HEAD"]},"role.store":{"uri":"join","methods":["POST"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"password.update":{"uri":"password","methods":["PUT"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
