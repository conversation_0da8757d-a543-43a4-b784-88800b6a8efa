{"__meta": {"id": "01K6YJZ5F4K3EGDJDQABDJE686", "datetime": "2025-10-07 05:40:36", "utime": **********.481391, "method": "GET", "uri": "/employer/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.142749, "end": **********.481438, "duration": 3.338688850402832, "duration_str": "3.34s", "measures": [{"label": "Booting", "start": **********.142749, "relative_start": 0, "end": **********.286845, "relative_end": **********.286845, "duration": 0.*****************, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.286875, "relative_start": 0.*****************, "end": **********.481443, "relative_end": 5.0067901611328125e-06, "duration": 3.***************, "duration_str": "3.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.348427, "relative_start": 0.*****************, "end": **********.349727, "relative_end": **********.349727, "duration": 0.0012998580932617188, "duration_str": "1.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.416896, "relative_start": 3.****************, "end": **********.451321, "relative_end": **********.451321, "duration": 0.034424781799316406, "duration_str": "34.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 4474640, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.32.5", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Employer/Dashboard", "param_count": null, "params": [], "start": **********.480314, "type": "react", "hash": "reactC:\\Users\\<USER>\\Desktop\\WorkWise\\resources\\js/Pages/Employer/Dashboard.jsxEmployer/Dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fresources%2Fjs%2FPages%2FEmployer%2FDashboard.jsx&line=1", "ajax": false, "filename": "Dashboard.jsx", "line": "?"}}]}, "queries": {"count": 152, "nb_statements": 151, "nb_visible_statements": 152, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.4102900000000002, "accumulated_duration_str": "410ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 51 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.384083, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "database.sqlite", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'KsZfqwJB6FXqqeqDnnzDwqTpHSWr6hZM8BAeIqHk' limit 1", "type": "query", "params": [], "bindings": ["KsZfqwJB6FXqqeqDnnzDwqTpHSWr6hZM8BAeIqHk"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.385066, "duration": 0.02914, "duration_str": "29.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database.sqlite", "explain": null, "start_percent": 0, "width_percent": 7.102}, {"sql": "select * from \"users\" where \"id\" = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.4473512, "duration": 0.00518, "duration_str": "5.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database.sqlite", "explain": null, "start_percent": 7.102, "width_percent": 1.263}, {"sql": "select \"status\", \"created_at\" from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 101}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.52038, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:101", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=101", "ajax": false, "filename": "EmployerDashboardController.php", "line": "101"}, "connection": "database.sqlite", "explain": null, "start_percent": 8.365, "width_percent": 0.28}, {"sql": "select \"gig_jobs\".*, (select count(*) from \"bids\" where \"gig_jobs\".\"id\" = \"bids\".\"job_id\") as \"bids_count\" from \"gig_jobs\" where \"employer_id\" = 2 order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 113}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.566525, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:113", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=113", "ajax": false, "filename": "EmployerDashboardController.php", "line": "113"}, "connection": "database.sqlite", "explain": null, "start_percent": 8.645, "width_percent": 0.346}, {"sql": "select * from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.602205, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:137", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=137", "ajax": false, "filename": "EmployerDashboardController.php", "line": "137"}, "connection": "database.sqlite", "explain": null, "start_percent": 8.991, "width_percent": 0.38}, {"sql": "select * from \"gig_jobs\" where \"gig_jobs\".\"id\" in (1, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 61}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.635566, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:137", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=137", "ajax": false, "filename": "EmployerDashboardController.php", "line": "137"}, "connection": "database.sqlite", "explain": null, "start_percent": 9.371, "width_percent": 0.278}, {"sql": "select * from \"users\" where \"users\".\"id\" in (5, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 61}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.660825, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:137", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=137", "ajax": false, "filename": "EmployerDashboardController.php", "line": "137"}, "connection": "database.sqlite", "explain": null, "start_percent": 9.649, "width_percent": 0.331}, {"sql": "select * from \"projects\" where \"employer_id\" = 2 and \"contract_signed\" = 1 and \"status\" in ('active', 'in_progress') order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [2, 1, "active", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 162}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 64}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.6852589, "duration": 0.011789999999999998, "duration_str": "11.79ms", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:162", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=162", "ajax": false, "filename": "EmployerDashboardController.php", "line": "162"}, "connection": "database.sqlite", "explain": null, "start_percent": 9.981, "width_percent": 2.874}, {"sql": "select * from \"notifications\" where \"user_id\" = 2 and \"type\" in ('escrow_status', 'deadline_approaching', 'message_received') order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [2, "escrow_status", "deadline_approaching", "message_received"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 148}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 185}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.7212229, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "NotificationManager.php:148", "source": {"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationManager.php&line=148", "ajax": false, "filename": "NotificationManager.php", "line": "148"}, "connection": "database.sqlite", "explain": null, "start_percent": 12.854, "width_percent": 0.824}, {"sql": "select * from \"notifications\" where \"user_id\" = 2 and \"type\" = 'escrow_status' and \"is_read\" = 0 order by \"created_at\" desc limit 3", "type": "query", "params": [], "bindings": [2, "escrow_status", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 176}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 186}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.74982, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "NotificationManager.php:176", "source": {"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationManager.php&line=176", "ajax": false, "filename": "NotificationManager.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 13.678, "width_percent": 0.356}, {"sql": "select * from \"contract_deadlines\" where exists (select * from \"projects\" where \"contract_deadlines\".\"contract_id\" = \"projects\".\"id\" and \"employer_id\" = 2) and \"due_date\" <= '2025-10-14' and \"due_date\" >= '2025-10-07' and \"status\" = 'pending' order by \"due_date\" asc limit 5", "type": "query", "params": [], "bindings": [2, "2025-10-14", "2025-10-07", "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 163}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 187}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.7804968, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "NotificationManager.php:163", "source": {"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationManager.php&line=163", "ajax": false, "filename": "NotificationManager.php", "line": "163"}, "connection": "database.sqlite", "explain": null, "start_percent": 14.034, "width_percent": 0.278}, {"sql": "select * from \"notifications\" where \"user_id\" = 2 and \"type\" = 'message_received' and \"is_read\" = 0 order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [2, "message_received", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 189}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.803251, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "NotificationManager.php:189", "source": {"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationManager.php&line=189", "ajax": false, "filename": "NotificationManager.php", "line": "189"}, "connection": "database.sqlite", "explain": null, "start_percent": 14.312, "width_percent": 0.212}, {"sql": "select count(*) as aggregate from \"notifications\" where \"user_id\" = 2 and \"is_read\" = 0", "type": "query", "params": [], "bindings": [2, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/NotificationService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationService.php", "line": 36}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 189}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.819528, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "NotificationService.php:36", "source": {"index": 16, "namespace": null, "name": "app/Services/NotificationService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationService.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationService.php&line=36", "ajax": false, "filename": "NotificationService.php", "line": "36"}, "connection": "database.sqlite", "explain": null, "start_percent": 14.524, "width_percent": 0.375}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 24}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.84831, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:39", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=39", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "39"}, "connection": "database.sqlite", "explain": null, "start_percent": 14.899, "width_percent": 0.197}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and \"status\" = 'open'", "type": "query", "params": [], "bindings": [2, "open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 24}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.867102, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:41", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=41", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "41"}, "connection": "database.sqlite", "explain": null, "start_percent": 15.097, "width_percent": 0.229}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and \"created_at\" >= '2025-09-07 05:40:33'", "type": "query", "params": [], "bindings": [2, "2025-09-07 05:40:33"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 24}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.897757, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:43", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=43", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "43"}, "connection": "database.sqlite", "explain": null, "start_percent": 15.326, "width_percent": 0.327}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and \"status\" = 'completed'", "type": "query", "params": [], "bindings": [2, "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 50}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 24}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.919875, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:50", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=50", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "50"}, "connection": "database.sqlite", "explain": null, "start_percent": 15.652, "width_percent": 0.19}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2)", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 61}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.98607, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:61", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=61", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "61"}, "connection": "database.sqlite", "explain": null, "start_percent": 15.842, "width_percent": 0.402}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and \"created_at\" >= '2025-09-30 05:40:34'", "type": "query", "params": [], "bindings": [2, "2025-09-30 05:40:34"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 65}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.0123441, "duration": 0.00891, "duration_str": "8.91ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:65", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=65", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "65"}, "connection": "database.sqlite", "explain": null, "start_percent": 16.245, "width_percent": 2.172}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 67}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.041147, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:67", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=67", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "67"}, "connection": "database.sqlite", "explain": null, "start_percent": 18.416, "width_percent": 0.28}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 68}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.066602, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:68", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=68", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "68"}, "connection": "database.sqlite", "explain": null, "start_percent": 18.697, "width_percent": 0.219}, {"sql": "select * from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2)", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 200}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.103888, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:200", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=200", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "200"}, "connection": "database.sqlite", "explain": null, "start_percent": 18.916, "width_percent": 0.899}, {"sql": "select * from \"users\" where \"users\".\"id\" in (5, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 200}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 74}, {"index": 22, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.127016, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:200", "source": {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=200", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "200"}, "connection": "database.sqlite", "explain": null, "start_percent": 19.815, "width_percent": 0.373}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 83}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.175437, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:83", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=83", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "83"}, "connection": "database.sqlite", "explain": null, "start_percent": 20.188, "width_percent": 0.268}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and \"status\" in ('active', 'in_progress')", "type": "query", "params": [], "bindings": [2, "active", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.1962702, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:85", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=85", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "85"}, "connection": "database.sqlite", "explain": null, "start_percent": 20.456, "width_percent": 0.451}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and \"status\" = 'completed'", "type": "query", "params": [], "bindings": [2, "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 87}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.234049, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:87", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=87", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "87"}, "connection": "database.sqlite", "explain": null, "start_percent": 20.907, "width_percent": 0.197}, {"sql": "select * from \"projects\" where \"employer_id\" = 2 and \"started_at\" is not null and \"completed_at\" is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 221}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 97}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.250503, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:221", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 221}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=221", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "221"}, "connection": "database.sqlite", "explain": null, "start_percent": 21.105, "width_percent": 0.258}, {"sql": "select sum(\"agreed_amount\") as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.2848861, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:106", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=106", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "106"}, "connection": "database.sqlite", "explain": null, "start_percent": 21.363, "width_percent": 0.375}, {"sql": "select sum(\"agreed_amount\") as aggregate from \"projects\" where \"employer_id\" = 2 and \"created_at\" >= '2025-10-01 00:00:00'", "type": "query", "params": [], "bindings": [2, "2025-10-01 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 108}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.319419, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:108", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=108", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "108"}, "connection": "database.sqlite", "explain": null, "start_percent": 21.738, "width_percent": 0.258}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 109}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.343369, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:109", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=109", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "109"}, "connection": "database.sqlite", "explain": null, "start_percent": 21.997, "width_percent": 0.253}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 110}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.366287, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:110", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=110", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "110"}, "connection": "database.sqlite", "explain": null, "start_percent": 22.25, "width_percent": 0.37}, {"sql": "select sum(\"agreed_amount\") as aggregate from \"projects\" where \"employer_id\" = 2 and \"created_at\" >= '2025-10-01 00:00:00'", "type": "query", "params": [], "bindings": [2, "2025-10-01 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 238}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 117}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.391184, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:238", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 238}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=238", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "238"}, "connection": "database.sqlite", "explain": null, "start_percent": 22.621, "width_percent": 0.375}, {"sql": "select sum(\"agreed_amount\") as aggregate from \"projects\" where \"employer_id\" = 2 and \"created_at\" between '2025-09-01 00:00:00' and '2025-09-30 23:59:59'", "type": "query", "params": [], "bindings": [2, "2025-09-01 00:00:00", "2025-09-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 244}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 117}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.418529, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:244", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=244", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "244"}, "connection": "database.sqlite", "explain": null, "start_percent": 22.996, "width_percent": 0.258}, {"sql": "select * from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and \"created_at\" >= '2025-09-07 05:40:34'", "type": "query", "params": [], "bindings": [2, "2025-09-07 05:40:34"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 129}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.4477332, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:129", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=129", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "129"}, "connection": "database.sqlite", "explain": null, "start_percent": 23.254, "width_percent": 0.324}, {"sql": "select * from \"gig_jobs\" where \"gig_jobs\".\"id\" in (1, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 129}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 28}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.471421, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:129", "source": {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=129", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "129"}, "connection": "database.sqlite", "explain": null, "start_percent": 23.578, "width_percent": 0.938}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 153}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.503972, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:153", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=153", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "153"}, "connection": "database.sqlite", "explain": null, "start_percent": 24.517, "width_percent": 0.2}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and exists (select * from \"projects\" where \"gig_jobs\".\"id\" = \"projects\".\"job_id\")", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.536662, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:155", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 155}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=155", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "155"}, "connection": "database.sqlite", "explain": null, "start_percent": 24.717, "width_percent": 0.241}, {"sql": "select \"gig_jobs\".*, (select count(*) from \"bids\" where \"gig_jobs\".\"id\" = \"bids\".\"job_id\") as \"bids_count\" from \"gig_jobs\" where \"employer_id\" = 2 and exists (select * from \"projects\" where \"gig_jobs\".\"id\" = \"projects\".\"job_id\")", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 261}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 161}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.566956, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:261", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 261}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=261", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "261"}, "connection": "database.sqlite", "explain": null, "start_percent": 24.958, "width_percent": 0.29}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 83}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 275}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 162}, {"index": 19, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}], "start": **********.585548, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:83", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=83", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "83"}, "connection": "database.sqlite", "explain": null, "start_percent": 25.248, "width_percent": 0.212}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and \"status\" in ('active', 'in_progress')", "type": "query", "params": [], "bindings": [2, "active", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 275}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 162}, {"index": 19, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}], "start": **********.604162, "duration": 0.00697, "duration_str": "6.97ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:85", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=85", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "85"}, "connection": "database.sqlite", "explain": null, "start_percent": 25.46, "width_percent": 1.699}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and \"status\" = 'completed'", "type": "query", "params": [], "bindings": [2, "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 87}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 275}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 162}, {"index": 19, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}], "start": **********.628256, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:87", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=87", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "87"}, "connection": "database.sqlite", "explain": null, "start_percent": 27.159, "width_percent": 0.324}, {"sql": "select * from \"projects\" where \"employer_id\" = 2 and \"started_at\" is not null and \"completed_at\" is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 221}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 97}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 275}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 162}, {"index": 19, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}], "start": **********.6434858, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:221", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 221}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=221", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "221"}, "connection": "database.sqlite", "explain": null, "start_percent": 27.483, "width_percent": 0.319}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-07' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.668617, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 27.802, "width_percent": 0.217}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-07' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.694191, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 28.019, "width_percent": 0.448}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-07' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.7206721, "duration": 0.00596, "duration_str": "5.96ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 28.468, "width_percent": 1.453}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-08' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.7404678, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 29.92, "width_percent": 0.319}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-08' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.7545571, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 30.24, "width_percent": 1.148}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-08' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.778159, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 31.388, "width_percent": 0.47}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-09' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.7965991, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 31.858, "width_percent": 0.241}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-09' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.813999, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 32.099, "width_percent": 0.341}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-09' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.83408, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 32.44, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-10' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.863544, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 32.689, "width_percent": 0.466}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-10' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.882892, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 33.155, "width_percent": 0.314}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-10' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.89829, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 33.469, "width_percent": 0.222}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-11' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.918221, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 33.691, "width_percent": 0.246}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-11' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.937695, "duration": 0.0061200000000000004, "duration_str": "6.12ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 33.937, "width_percent": 1.492}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-11' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.959284, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 35.429, "width_percent": 0.307}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-12' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.9846148, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 35.736, "width_percent": 0.349}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-12' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.0140522, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 36.084, "width_percent": 0.451}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-12' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.053689, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 36.535, "width_percent": 1.189}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-13' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.0708911, "duration": 0.00536, "duration_str": "5.36ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 37.725, "width_percent": 1.306}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-13' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.097386, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 39.031, "width_percent": 0.263}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-13' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.115095, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 39.294, "width_percent": 0.266}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-14' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.131496, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 39.56, "width_percent": 0.446}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-14' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.150772, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 40.006, "width_percent": 0.297}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-14' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.167931, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 40.303, "width_percent": 0.288}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-15' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.196997, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 40.591, "width_percent": 0.222}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-15' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.214071, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 40.813, "width_percent": 0.329}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-15' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.231263, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 41.142, "width_percent": 0.219}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-16' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.258329, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 41.361, "width_percent": 0.49}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-16' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.275263, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 41.851, "width_percent": 0.385}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-16' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.294898, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 42.236, "width_percent": 0.509}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-17' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.384774, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 42.745, "width_percent": 0.292}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-17' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.40461, "duration": 0.03625, "duration_str": "36.25ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 43.038, "width_percent": 8.835}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-17' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.451924, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 51.873, "width_percent": 0.197}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-18' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.4675071, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 52.07, "width_percent": 0.212}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-18' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.484707, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 52.283, "width_percent": 0.236}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-18' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.5000918, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 52.519, "width_percent": 0.232}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-19' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.53143, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 52.75, "width_percent": 0.534}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-19' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.550998, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 53.284, "width_percent": 0.275}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-19' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.569169, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 53.56, "width_percent": 0.212}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-20' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.5860639, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 53.772, "width_percent": 0.261}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-20' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.60333, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 54.033, "width_percent": 0.266}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-20' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.6269028, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 54.298, "width_percent": 0.271}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-21' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.657525, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 54.569, "width_percent": 0.37}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-21' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.682766, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 54.939, "width_percent": 0.253}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-21' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.723983, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 55.193, "width_percent": 0.217}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-22' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.7452168, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 55.41, "width_percent": 0.229}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-22' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.7659009, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 55.639, "width_percent": 0.317}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-22' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.792644, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 55.956, "width_percent": 0.331}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-23' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.819929, "duration": 0.009519999999999999, "duration_str": "9.52ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 56.287, "width_percent": 2.32}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-23' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.853406, "duration": 0.0062900000000000005, "duration_str": "6.29ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 58.607, "width_percent": 1.533}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-23' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.887291, "duration": 0.00943, "duration_str": "9.43ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 60.14, "width_percent": 2.298}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-24' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.916188, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 62.439, "width_percent": 0.273}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-24' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.94434, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 62.712, "width_percent": 0.427}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-24' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.9829762, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 63.138, "width_percent": 0.327}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-25' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.002625, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 63.465, "width_percent": 0.244}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-25' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.025242, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 63.709, "width_percent": 0.331}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-25' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.042695, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 64.04, "width_percent": 0.305}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-26' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-26"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.064847, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 64.345, "width_percent": 0.268}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.096822, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 64.613, "width_percent": 0.353}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.099252, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 64.966, "width_percent": 0.317}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1114721, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 65.283, "width_percent": 0.324}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.11484, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 65.607, "width_percent": 0.366}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.117326, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 65.973, "width_percent": 0.241}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.119331, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 66.214, "width_percent": 0.271}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.127777, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 66.485, "width_percent": 0.417}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1305401, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 66.901, "width_percent": 0.283}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.135345, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 67.184, "width_percent": 0.341}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1441221, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 67.525, "width_percent": 0.288}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.146488, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 67.813, "width_percent": 0.551}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.150207, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 68.364, "width_percent": 0.597}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.158564, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 68.961, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.160389, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 69.21, "width_percent": 0.222}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.162175, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 69.431, "width_percent": 0.21}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.165183, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 69.641, "width_percent": 0.302}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1672642, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 69.943, "width_percent": 0.236}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.169133, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 70.18, "width_percent": 0.214}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.173665, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 70.394, "width_percent": 0.383}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.176196, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 70.777, "width_percent": 0.239}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1782448, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 71.016, "width_percent": 0.229}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1814148, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 71.245, "width_percent": 0.275}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.183434, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 71.52, "width_percent": 0.219}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.185318, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 71.74, "width_percent": 0.214}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.190662, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 71.954, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.193189, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 72.359, "width_percent": 0.358}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.196276, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 72.717, "width_percent": 0.331}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.199566, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 73.048, "width_percent": 0.319}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2018669, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 73.368, "width_percent": 0.234}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.203926, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 73.602, "width_percent": 0.814}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2092252, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 74.416, "width_percent": 0.409}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.212031, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 74.825, "width_percent": 0.573}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.217391, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 75.398, "width_percent": 0.219}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" between ? and ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.220669, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 75.617, "width_percent": 1.024}, {"sql": "select \"gig_jobs\".*, (select count(*) from \"bids\" where \"gig_jobs\".\"id\" = \"bids\".\"job_id\") as \"bids_count\" from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" >= ? order by \"created_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.228345, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 76.641, "width_percent": 0.578}, {"sql": "select * from \"projects\" where \"employer_id\" = ? and \"contract_signed\" = ? and \"contract_signed_at\" >= ? order by \"contract_signed_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.249007, "duration": 0.052219999999999996, "duration_str": "52.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 77.219, "width_percent": 12.728}, {"sql": "select * from \"projects\" where \"employer_id\" = ? and \"payment_released\" = ? and \"payment_released_at\" >= ? order by \"payment_released_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.302628, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 89.946, "width_percent": 0.29}, {"sql": "select * from \"contract_deadlines\" where exists (select * from \"projects\" where \"contract_deadlines\".\"contract_id\" = \"projects\".\"id\" and \"employer_id\" = ?) and \"status\" = ? and \"updated_at\" >= ? order by \"updated_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.308588, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 90.236, "width_percent": 0.314}, {"sql": "select * from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and \"created_at\" >= ? order by \"created_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3133068, "duration": 0.01816, "duration_str": "18.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 90.551, "width_percent": 4.426}, {"sql": "select * from \"gig_jobs\" where \"gig_jobs\".\"id\" in (1, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.33476, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 94.977, "width_percent": 0.258}, {"sql": "select * from \"users\" where \"users\".\"id\" in (5, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.337875, "duration": 0.00478, "duration_str": "4.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 95.235, "width_percent": 1.165}, {"sql": "select * from \"notifications\" where \"user_id\" = ? and \"created_at\" >= ? and \"type\" in (?, ?) order by \"created_at\" desc limit 2", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372724, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 96.4, "width_percent": 0.478}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3943691, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 96.878, "width_percent": 0.366}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397477, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 97.243, "width_percent": 0.256}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"contract_signed_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3995218, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 97.499, "width_percent": 0.19}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4009428, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 97.689, "width_percent": 0.185}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402975, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 97.875, "width_percent": 0.205}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and \"contract_signed_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4045029, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 98.079, "width_percent": 1.077}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4098182, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 99.157, "width_percent": 0.202}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411809, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 99.359, "width_percent": 0.439}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and \"contract_signed_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414288, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 99.798, "width_percent": 0.202}]}, "models": {"data": {"App\\Models\\GigJob": {"retrieved": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FGigJob.php&line=1", "ajax": false, "filename": "GigJob.php", "line": "?"}}, "App\\Models\\Bid": {"retrieved": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FBid.php&line=1", "ajax": false, "filename": "Bid.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 32, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 32}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/employer/dashboard", "action_name": "employer.dashboard", "controller_action": "App\\Http\\Controllers\\EmployerDashboardController@index", "uri": "GET employer/dashboard", "controller": "App\\Http\\Controllers\\EmployerDashboardController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=48\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=48\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/EmployerDashboardController.php:48-95</a>", "middleware": "web, auth, verified", "duration": "3.7s", "peak_memory": "6MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1525451574 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1525451574\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1985032716 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1985032716\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-169774960 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjhOV2xJa2NQaXprcWlNNlJoWi85T3c9PSIsInZhbHVlIjoiZlIrZlJDR0JUM2xRRWlFZU9uclJFWldsbDdORk9pUTR4YWY5ZWQ0ZVJ1MFptODNoaHdIMndvUURPbjVUN2tBSU5jeXAyTjFucEFuWk9TMkNnK1pWaUtkWTZ1c2ZNQVBDekZHNWdzNTB0VHdJakpiUWY3L3laRjlOUGlsY2VxM3MiLCJtYWMiOiJjYzgyNzEyM2NjYjQ1YmQ5MGRmMzk0NDQ3ZmRhMmNlZTI3ZGViMDUyYmE1NGMwNmE2ZDhmNDJmMDBjMGQwOTc0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6Imt4MHhVS0ZLWGxzRVhleVVoVVF3Zmc9PSIsInZhbHVlIjoicFc3MjN3VG1odzJ4ZExGUnBCTGdMUTlHa0FIOGFYbGc1ajZWWFFrUnY4SUlpNjI2SUhiaURtK1VGNnpSYnhzUUZtbzNTNyt4cXlnMHBlRkdidEE3aFV5bWVOVVI3WHlwL2VlTWVVaFhGVGdpZ2ZXSTlrdXltMnFhM1p3Q1Q5MHEiLCJtYWMiOiJkMzc4MjBhN2Y2ZWVhOWNmMmJlMGM4ZWJkMjNlMjMzNjFiMjQ2ZjU3YTRiZGRlYjQ3ZWFkNjcwNGUzZTIyMGZhIiwidGFnIjoiIn0%3D; workwise_session=eyJpdiI6InMwUzF2blFVNjBzSlYxczZJSk5EbGc9PSIsInZhbHVlIjoidnhLRzFzYUUwZ3hqR2UwSlZlMk05UCtnN1RTTGU5Q3hoTmpjeG9WL0RmV1lwczRpUkg1dnZWRDFOeTFEWk9JZGZUUzAxN1E5UEZ6Mk9GQnlRTXM1NEdaa3I3U3VZMEQ0N2dJZFVxN29OTi9pcHRISTdIaFQyVXF1ZEJ3MUZDcCsiLCJtYWMiOiIyMWE1MWFhYjU3ZjlkZTZiNTY3OTVhODdjYTkyMDgzM2M4MDU4ZTA3M2FkMzAyOGE3MTg3MWNmYTI2ZWFjYzcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169774960\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-198457422 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">V8CEQ6BjfyNFplbxjZlRZR18PnQaTPTwpQRrp0dO</span>\"\n  \"<span class=sf-dump-key>workwise_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KsZfqwJB6FXqqeqDnnzDwqTpHSWr6hZM8BAeIqHk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198457422\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1294243779 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 07 Oct 2025 05:40:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294243779\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1464484109 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">V8CEQ6BjfyNFplbxjZlRZR18PnQaTPTwpQRrp0dO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K6YJZ1HN34BWE2DFG3F4W6A4</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464484109\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/employer/dashboard", "action_name": "employer.dashboard", "controller_action": "App\\Http\\Controllers\\EmployerDashboardController@index"}, "badge": null}}