<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Freelancer Platform</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<style>
        body {
            font-family: 'Manrope', sans-serif;
        }
        #chat-widget.minimized {
            height: 64px;
            width: 64px;
            border-radius: 9999px;
            overflow: hidden;
        }
        #chat-widget.minimized header {
            display: none;
        }
        #chat-widget.minimized main, #chat-widget.minimized footer {
            display: none;
        }
        #chat-widget.minimized .minimized-view {
            display: flex;
        }
    </style>
</head>
<body class="bg-gray-100">
<div class="relative min-h-screen">
<div class="container mx-auto p-8">
<h1 class="text-4xl font-bold mb-4">Find the perfect freelancer</h1>
<p class="text-lg text-gray-600 mb-8">Browse our community of talented professionals to get your project done.</p>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
<div class="bg-white rounded-lg shadow-md p-6">
<h3 class="text-xl font-semibold mb-2">Web Development</h3>
<p class="text-gray-700">Get a stunning website that represents your brand and converts visitors into customers.</p>
</div>
<div class="bg-white rounded-lg shadow-md p-6">
<h3 class="text-xl font-semibold mb-2">Graphic Design</h3>
<p class="text-gray-700">Logos, branding, and marketing materials that make an impact.</p>
</div>
<div class="bg-white rounded-lg shadow-md p-6">
<h3 class="text-xl font-semibold mb-2">Content Writing</h3>
<p class="text-gray-700">Engaging articles, blog posts, and copy that tells your story.</p>
</div>
</div>
</div>
<div class="fixed bottom-4 right-4 w-96 h-[500px] bg-white rounded-xl shadow-2xl flex flex-col group/design-root overflow-hidden border border-gray-200 transition-all duration-300 minimized" id="chat-widget">
<header class="flex items-center gap-3 bg-white px-4 py-3 border-b border-gray-200 cursor-pointer" onclick="document.getElementById('chat-widget').classList.toggle('minimized')">
<div class="relative">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-10 w-10" data-alt="John Doe's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDt1i9sut1CqT13M_WeiTgWds1bcPjcyp_1o8DjJgqxyqg-1m9oybg2RTmd3cssYFRVLxEecL4-f-ZGdzFPmpldTYgtrlPn2hmYFP-L-R_WzVLdb8iP_lrWiANZ8djQH6YWVarxZDegJcTPifjVC5dweUh1uKStjJvj-OdtjF7sBmbUA7OXtZtQrdrih9HjIkP_OxPIMfd7ZqbF1QMxCUfCcasoOZw6azz28MDl0IDB-__9AHG2DZK37tmKgEN7fhm15Zx4nSOGwQ");'></div>
<div class="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-white"></div>
</div>
<div class="flex-1">
<p class="text-[#0e1a13] text-base font-semibold leading-normal">John Doe</p>
<p class="text-[#6C757D] text-xs" id="chat-status-text">Online</p>
</div>
<button class="text-[#6C757D] flex items-center justify-center p-2 rounded-full hover:bg-gray-100" onclick="event.stopPropagation(); document.getElementById('chat-widget').classList.toggle('minimized')">
<span class="material-symbols-outlined text-base transition-transform duration-300" id="minimize-icon">expand_more</span>
</button>
<button class="text-[#6C757D] flex items-center justify-center p-2 rounded-full hover:bg-gray-100" onclick="event.stopPropagation(); document.getElementById('chat-widget').style.display='none'">
<span class="material-symbols-outlined text-base">close</span>
</button>
</header>
<main class="flex-1 overflow-y-auto p-4 space-y-4">
<div class="text-center my-2">
<span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Today</span>
</div>
<div class="flex items-end gap-2">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-8 shrink-0" data-alt="John Doe's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCpT8nY21HzlAbmtjAGZQHzoKKZRCrFKv2Xej9k6OxFlDVoU5hkQAtUMNHni8E48elM1BJQZ4dk5oa8xGGi9nfKMPbIQD0pyFYvAZPJ8BLlTNemcT-zBOMguwz4A8CUx9PEClP5zkNwsavl93k0h_CTX9xMeStUjOaoNTxKtkajOeYqJNGzkG7UtGi8O7r9E9WzCWViZOGQ6M1ypB966uG8G-5SyPimij46zeXylPvCoN4siGV1WUoxFhsdBvBlHDZA-ndFKyY5SA");'></div>
<div class="flex flex-1 flex-col gap-1 items-start">
<p class="text-sm font-normal leading-normal flex max-w-[280px] rounded-lg px-3 py-2 bg-gray-100 text-[#0e1a13] rounded-tl-none">Hi, I have a question about the project. I'm reviewing the design brief and I need some clarification on the target audience.</p>
<p class="text-gray-500 text-xs">10:30 AM</p>
</div>
</div>
<div class="flex items-end gap-2 justify-end">
<div class="flex flex-1 flex-col gap-1 items-end">
<p class="text-sm font-normal leading-normal flex max-w-[280px] rounded-lg px-3 py-2 bg-[#007BFF] text-white rounded-br-none">Sure, what is it?</p>
<p class="text-gray-500 text-xs">10:32 AM</p>
</div>
</div>
<div class="flex items-end gap-2">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-8 shrink-0" data-alt="John Doe's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuALsfaejldew_6HD3Y9cnnq0Aeibr6wnyJGUKBcgosB0bbIUxzq5v2rU1x9pYBkiVx9-JCOF-as0KBGBRzeWRmN9o2LxN9VzZ0pTN_LecHCOey1p1Kz2s0QgNDdYL6-9ox-GczKMK6lFZxwYE8Gbl6O0Zz2rhw6T0Ow8ZnhZyl0ercSM8oibxGWOpv5Ol6W-Mp_lFKjlHh41wJaM3Ducp27RLFto9HPQ7eD06jt2Z-CjMwMAy1DHBSmp1Ng1qOe7uYyAzOG6ov7Uw");'></div>
<div class="flex flex-1 flex-col gap-1 items-start">
<p class="text-sm font-normal leading-normal flex max-w-[280px] rounded-lg px-3 py-2 bg-gray-100 text-[#0e1a13] rounded-tl-none">The brief mentions "young professionals," but that's quite broad. Could we narrow it down to a specific age range or industry?</p>
<p class="text-gray-500 text-xs">10:33 AM</p>
</div>
</div>
<div class="flex items-end gap-2 justify-end">
<div class="flex flex-1 flex-col gap-1 items-end">
<p class="text-sm font-normal leading-normal flex max-w-[280px] rounded-lg px-3 py-2 bg-[#007BFF] text-white rounded-br-none">Good question. Let's target tech professionals aged 25-35. That should give us a more focused scope.</p>
<p class="text-gray-500 text-xs">10:35 AM</p>
</div>
</div>
<div class="flex items-end gap-2">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-8 shrink-0" data-alt="John Doe's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDnZv4WAQhb9y-vbC1sXVy-L259S6udc9qYk4vBpjMGAPk9l_pwBOP9cXHImG1c6ZgWJ3WDvjF3MTg2EQizHU0HoesQMhmvja34X4NCiyF51hrGELnXiCA3zPfTBDWLMk7GD2FlODX_5JrSgTym0qaPTui4n1xnyx0lYRh5Ye_L2_ouhseyASHlFT3WB6OoDMpAV7j3Go4-cEl79i8R2iAetyCMHi5a96KWtyADUIxInyy-qnn5qgu65D18JE9W6khrFMRmCUY61Q");'></div>
<div class="flex flex-1 flex-col gap-1 items-start">
<p class="text-sm font-normal leading-normal flex max-w-[280px] rounded-lg px-3 py-2 bg-gray-100 text-[#0e1a13] rounded-tl-none">Perfect, that's much clearer. I'll get started on the initial concepts. Also, I've attached the project timeline for your review.</p>
<div class="flex items-center gap-2 p-2 rounded-lg border border-gray-200 bg-white w-full mt-1">
<span class="material-symbols-outlined text-[#007BFF]">description</span>
<div class="flex-1">
<p class="text-xs font-medium text-[#0e1a13]">Project_Timeline.pdf</p>
<p class="text-xs text-[#6C757D]">1.2 MB</p>
</div>
<button class="p-1 text-[#6C757D] hover:text-[#007BFF]">
<span class="material-symbols-outlined text-base">download</span>
</button>
</div>
<p class="text-gray-500 text-xs mt-1">10:36 AM</p>
</div>
</div>
</main>
<footer class="bg-white p-3 border-t border-gray-200">
<div class="flex items-center gap-2">
<div class="flex-1 relative">
<input class="form-input w-full rounded-full bg-gray-100 border-transparent focus:ring-1 focus:ring-[#007BFF] focus:bg-white text-sm px-4 py-2" placeholder="Type a message..." value=""/>
<button class="absolute right-2 top-1/2 -translate-y-1/2 p-1 text-gray-500 hover:text-[#007BFF]">
<span class="material-symbols-outlined text-lg">attachment</span>
</button>
</div>
<button class="flex-shrink-0 cursor-pointer items-center justify-center rounded-full h-9 w-9 bg-[#007BFF] hover:bg-blue-600 text-white transition-colors flex">
<span class="material-symbols-outlined text-lg">send</span>
</button>
</div>
</footer>
<div class="minimized-view hidden w-full h-full items-center justify-center cursor-pointer" onclick="document.getElementById('chat-widget').classList.remove('minimized')">
<div class="relative">
<span class="material-symbols-outlined text-3xl text-gray-600">chat_bubble</span>
<div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white"></div>
</div>
</div>
</div>
</div>
<script>
    const chatWidget = document.getElementById('chat-widget');
    const minimizeIcon = document.getElementById('minimize-icon');
    const chatStatusText = document.getElementById('chat-status-text');
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.attributeName === 'class') {
                const isMinimized = chatWidget.classList.contains('minimized');
                minimizeIcon.style.transform = isMinimized ? 'rotate(180deg)' : 'rotate(0deg)';
                minimizeIcon.innerHTML = isMinimized ? 'expand_less' : 'expand_more';
                chatStatusText.textContent = isMinimized ? 'Click to expand' : 'Online';
            }
        });
    });
    observer.observe(chatWidget, { attributes: true });
</script>

</body></html>