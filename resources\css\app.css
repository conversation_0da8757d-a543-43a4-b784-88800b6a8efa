@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Admin Dashboard Styles */
:root {
    --primary: #4f46e5;
    --secondary: #ec4899;
    --accent: #10b981;
    --background-light: #f8fafc;
    --background-dark: #0f172a;
    --text-light: #f1f5f9;
    --text-dark: #020617;
    --card-light: #ffffff;
    --card-dark: #1e293b;
}

.icon-hover {
    @apply transition-transform duration-300 ease-in-out;
}

.icon-hover:hover {
    @apply scale-110 -rotate-6;
}

/* Dark mode styles */
.dark {
    --background-light: #0f172a;
    --background-dark: #020617;
    --text-light: #f1f5f9;
    --text-dark: #ffffff;
    --card-light: #1e293b;
    --card-dark: #334155;
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Material Symbols font */
.material-symbols-outlined {
    font-family: 'Material Symbols Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

/* Prevent text selection and cursor issues on non-editable elements */
* {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Allow text selection for input fields, textareas, and editable content */
input, textarea, [contenteditable="true"], [contenteditable="plaintext-only"] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Ensure proper cursor behavior */
button, .cursor-pointer, [role="button"] {
    cursor: pointer !important;
}

a, .cursor-pointer {
    cursor: pointer !important;
}

/* Prevent text cursor on non-interactive elements */
div, span, p, h1, h2, h3, h4, h5, h6, label {
    cursor: default;
}

/* Allow text cursor for editable content */
[contenteditable="true"], [contenteditable="plaintext-only"] {
    cursor: text !important;
}

/* Notification Dropdown Styles */
.notifications-dropdown {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    max-height: 400px;
    overflow: hidden;
}

.notifications-dropdown .notification-item {
    transition: all 0.2s ease-in-out;
}

.notifications-dropdown .notification-item:hover {
    background-color: #f9fafb;
    transform: translateX(2px);
}

.notifications-dropdown .notification-item.unread {
    background-color: #eff6ff;
    border-left: 3px solid #3b82f6;
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: #ef4444;
    color: white;
    font-size: 10px;
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2px;
    line-height: 1;
    z-index: 10;
}

/* Ensure dropdown is above other elements */
.notifications-dropdown {
    z-index: 9999 !important;
}

.messages-dropdown {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    max-height: 400px;
    overflow: hidden;
}

.messages-dropdown .conversation-item {
    transition: all 0.2s ease-in-out;
}

.messages-dropdown .conversation-item:hover {
    background-color: #f9fafb;
    transform: translateX(2px);
}

.messages-dropdown .conversation-item.unread {
    background-color: #eff6ff;
    border-left: 3px solid #3b82f6;
}
