@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Admin Dashboard Styles */
:root {
    --primary: #4f46e5;
    --secondary: #ec4899;
    --accent: #10b981;
    --background-light: #f8fafc;
    --background-dark: #0f172a;
    --text-light: #f1f5f9;
    --text-dark: #020617;
    --card-light: #ffffff;
    --card-dark: #1e293b;
}

.icon-hover {
    @apply transition-transform duration-300 ease-in-out;
}

.icon-hover:hover {
    @apply scale-110 -rotate-6;
}

/* Dark mode styles */
.dark {
    --background-light: #0f172a;
    --background-dark: #020617;
    --text-light: #f1f5f9;
    --text-dark: #ffffff;
    --card-light: #1e293b;
    --card-dark: #334155;
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Material Symbols font */
.material-symbols-outlined {
    font-family: 'Material Symbols Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

/* Prevent text selection and cursor issues on non-editable elements */
* {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Allow text selection for input fields, textareas, and editable content */
input, textarea, [contenteditable="true"], [contenteditable="plaintext-only"] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Ensure proper cursor behavior */
button, .cursor-pointer, [role="button"] {
    cursor: pointer !important;
}

a, .cursor-pointer {
    cursor: pointer !important;
}

/* Prevent text cursor on non-interactive elements */
div, span, p, h1, h2, h3, h4, h5, h6, label {
    cursor: default;
}

/* Allow text cursor for editable content */
[contenteditable="true"], [contenteditable="plaintext-only"] {
    cursor: text !important;
}

/* Enhanced Notification Dropdown Styles */
.notifications-dropdown {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    max-height: 500px;
    overflow: hidden;
    z-index: 9999 !important;
    backdrop-filter: blur(8px);
}

/* Custom scrollbar for notifications */
.scrollbar-thin {
    scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Line clamp utility for notification text */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Enhanced notification item animations */
.notification-item {
    position: relative;
    overflow: hidden;
}

.notification-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.notification-item:hover::before {
    left: 100%;
}

/* Pulse animation for new notifications */
@keyframes notification-pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

.animate-notification-pulse {
    animation: notification-pulse 2s infinite;
}

/* Loading spinner animations */
@keyframes spin-reverse {
    from {
        transform: rotate(360deg);
    }
    to {
        transform: rotate(0deg);
    }
}

.animate-spin-reverse {
    animation: spin-reverse 1.5s linear infinite;
}

/* Skeleton loading animation */
@keyframes skeleton-loading {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Message animations */
@keyframes message-slide-in {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes message-fade-in {
    0% {
        opacity: 0;
        transform: translateX(-10px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes typing-indicator {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

@keyframes message-sending {
    0% {
        opacity: 0.6;
        transform: scale(0.98);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.01);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Message bubble styles */
.message-bubble {
    animation: message-slide-in 0.3s ease-out;
}

.message-bubble.optimistic {
    animation: message-sending 1s ease-in-out infinite;
}

.message-bubble.received {
    animation: message-fade-in 0.4s ease-out;
}

/* Typing indicator */
.typing-dot {
    animation: typing-indicator 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
    animation-delay: -0.16s;
}

/* Message input enhancements */
.message-input-container {
    transition: all 0.2s ease-in-out;
}

.message-input-container:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Conversation list animations */
.conversation-item {
    transition: all 0.2s ease-in-out;
}

.conversation-item:hover {
    transform: translateX(4px);
}

/* File attachment preview */
.file-preview {
    animation: message-slide-in 0.3s ease-out;
}

/* Message status indicators */
@keyframes status-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.message-status.sending {
    animation: status-pulse 1s ease-in-out infinite;
}

/* Enhanced scrollbar for message areas */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #cbd5e1, #94a3b8);
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #94a3b8, #64748b);
}

.animate-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
}

.messages-dropdown {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    max-height: 500px;
    overflow: hidden;
    z-index: 9999 !important;
    backdrop-filter: blur(8px);
}

.messages-dropdown .conversation-item {
    transition: all 0.2s ease-in-out;
}

.messages-dropdown .conversation-item:hover {
    background-color: #f9fafb;
    transform: translateX(2px);
}

.messages-dropdown .conversation-item.unread {
    background-color: #eff6ff;
    border-left: 3px solid #3b82f6;
}

/* Messages dropdown animations */
@keyframes dropdown-slide-in {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.messages-dropdown {
    animation: dropdown-slide-in 0.2s ease-out;
}

/* Enhanced conversation hover effects */
.messages-dropdown .conversation-item {
    position: relative;
    overflow: hidden;
}

.messages-dropdown .conversation-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s ease-in-out;
}

.messages-dropdown .conversation-item:hover::before {
    left: 100%;
}

/* Avatar pulse animation for online status */
@keyframes avatar-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0);
    }
}

.avatar-online {
    animation: avatar-pulse 2s infinite;
}

/* Message count badge animation */
@keyframes badge-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-1px);
    }
}

.message-count-badge {
    animation: badge-bounce 0.6s ease-in-out;
}
