<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Post a Job</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style type="text/tailwindcss">
    :root {
      --primary-blue: #3B82F6;
      --primary-green: #10B981;
      --light-gray: #F3F4F6;
      --medium-gray: #E5E7EB;
      --dark-gray: #374151;
      --text-primary: #1F2937;
      --text-secondary: #6B7280;
    }
    body {
      font-family: 'Inter', sans-serif;
    }
    .slide-out-left {
      animation: slide-out-left 0.5s forwards;
    }
    .slide-in-right {
      animation: slide-in-right 0.5s forwards;
    }
    .slide-in-left {
      animation: slide-in-left 0.5s forwards;
    }
    .slide-out-right {
      animation: slide-out-right 0.5s forwards;
    }
    @keyframes slide-out-left {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(-100%);
        opacity: 0;
      }
    }
    @keyframes slide-in-right {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    @keyframes slide-in-left {
      from {
        transform: translateX(-100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    @keyframes slide-out-right {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }
  </style>
</head>
<body class="bg-gray-100">
<div aria-labelledby="modal-title" aria-modal="true" class="fixed inset-0 z-10 overflow-y-auto bg-black bg-opacity-50" role="dialog">
<div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
<span aria-hidden="true" class="hidden sm:inline-block sm:align-middle sm:h-screen">​</span>
<div class="inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:max-w-4xl sm:w-full">
<div class="px-6 pt-5 pb-4 sm:p-8">
<div class="flex items-start justify-between pb-4 border-b border-gray-200">
<div>
<h3 class="text-xl leading-6 font-semibold text-gray-900" id="modal-title">
                Post a Job
              </h3>
<p class="mt-1 text-sm text-gray-500">Fill in the details below to find the perfect candidate.</p>
</div>
<button class="text-gray-400 hover:text-gray-600 focus:outline-none" type="button">
<span class="sr-only">Close</span>
<span class="material-icons">close</span>
</button>
</div>
<div class="py-6">
<div class="overflow-hidden">
<div class="w-full transition-transform duration-500 ease-in-out" id="section1">
<form action="#" class="space-y-8" method="POST">
<div>
<label class="block text-sm font-medium text-gray-700 mb-2" for="job-title">Job Title *</label>
<input class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="job-title" name="job-title" placeholder="e.g., Senior Frontend Developer" type="text"/>
<p class="mt-2 text-sm text-gray-500">Write a clear, descriptive title.</p>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-2" for="job-description">Job Description *</label>
<textarea class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="job-description" name="job-description" placeholder="Describe the role, responsibilities, and requirements..." rows="6"></textarea>
<p class="mt-2 text-sm text-gray-500">Provide a detailed description of the job.</p>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-2" for="required-skills">Required Skills *</label>
<div class="flex items-center space-x-2">
<input class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="required-skills" name="required-skills" placeholder="Type a skill and press Enter" type="text"/>
<button class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" type="button">Add</button>
</div>
<div class="mt-3 flex flex-wrap gap-2">
<span class="inline-flex items-center py-1.5 px-3 rounded-full text-sm font-medium bg-blue-100 text-blue-800">React.js <button class="ml-1.5 text-blue-500 hover:text-blue-700">×</button></span>
<span class="inline-flex items-center py-1.5 px-3 rounded-full text-sm font-medium bg-green-100 text-green-800">Node.js <button class="ml-1.5 text-green-500 hover:text-green-700">×</button></span>
</div>
</div>
</form>
</div>
<div class="w-full transition-transform duration-500 ease-in-out hidden" id="section2">
<form action="#" class="space-y-8" method="POST">
<div>
<label class="block text-sm font-medium text-gray-700 mb-3">Budget *</label>
<div class="flex items-center space-x-6">
<div class="flex items-center">
<input checked="" class="focus:ring-green-500 h-4 w-4 text-green-600 border-gray-300" id="fixed-price" name="budget-type" type="radio"/>
<label class="ml-3 block text-sm font-medium text-gray-700" for="fixed-price">Fixed Price</label>
</div>
<div class="flex items-center">
<input class="focus:ring-green-500 h-4 w-4 text-green-600 border-gray-300" id="hourly-rate" name="budget-type" type="radio"/>
<label class="ml-3 block text-sm font-medium text-gray-700" for="hourly-rate">Hourly Rate</label>
</div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
<div>
<label class="block text-sm font-medium text-gray-700 mb-2" for="min-budget">Minimum</label>
<div class="relative rounded-md shadow-sm">
<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
<span class="text-gray-500 sm:text-sm">$</span>
</div>
<input class="block w-full rounded-md border-gray-300 pl-7 pr-12 focus:border-green-500 focus:ring-green-500" id="min-budget" name="min-budget" placeholder="0.00" type="number"/>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-2" for="max-budget">Maximum</label>
<div class="relative rounded-md shadow-sm">
<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
<span class="text-gray-500 sm:text-sm">$</span>
</div>
<input class="block w-full rounded-md border-gray-300 pl-7 pr-12 focus:border-green-500 focus:ring-green-500" id="max-budget" name="max-budget" placeholder="0.00" type="number"/>
</div>
</div>
</div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
<div>
<label class="block text-sm font-medium text-gray-700 mb-2" for="experience-level">Experience Level *</label>
<select class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="experience-level" name="experience-level">
<option>Beginner (0-1 years)</option>
<option selected="">Intermediate (2-5 years)</option>
<option>Expert (5+ years)</option>
</select>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-2" for="estimated-duration">Estimated Duration (Days) *</label>
<input class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="estimated-duration" name="estimated-duration" placeholder="e.g., 30" type="number"/>
</div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 mb-2" for="project-deadline">Project Deadline (Optional)</label>
<div class="relative">
<input class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" id="project-deadline" name="project-deadline" placeholder="mm/dd/yyyy" type="date"/>
</div>
<p class="mt-2 text-sm text-gray-500">When do you need this project completed?</p>
</div>
</form>
</div>
</div>
</div>
</div>
<div class="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
<button class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-6 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm" id="nextBtn" type="button">
            Next
          </button>
<button class="hidden w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-6 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm" id="postBtn" type="button">
            Post Job
          </button>
<button class="hidden mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-6 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm" id="backBtn" type="button">
            Back
          </button>
<button class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-6 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm" id="cancelBtn" type="button">
            Cancel
          </button>
</div>
</div>
</div>
</div>
<script>
    const section1 = document.getElementById('section1');
    const section2 = document.getElementById('section2');
    const nextBtn = document.getElementById('nextBtn');
    const backBtn = document.getElementById('backBtn');
    const postBtn = document.getElementById('postBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    nextBtn.addEventListener('click', () => {
      section1.classList.add('slide-out-left');
      section1.addEventListener('animationend', () => {
        section1.classList.add('hidden');
        section1.classList.remove('slide-out-left');
        section2.classList.remove('hidden');
        section2.classList.add('slide-in-right');
      }, {
        once: true
      });
      nextBtn.classList.add('hidden');
      postBtn.classList.remove('hidden');
      backBtn.classList.remove('hidden');
      cancelBtn.classList.add('hidden');
    });
    backBtn.addEventListener('click', () => {
      section2.classList.add('slide-out-right');
      section2.addEventListener('animationend', () => {
        section2.classList.add('hidden');
        section2.classList.remove('slide-out-right');
        section1.classList.remove('hidden');
        section1.classList.add('slide-in-left');
      }, {
        once: true
      });
      nextBtn.classList.remove('hidden');
      postBtn.classList.add('hidden');
      backBtn.classList.add('hidden');
      cancelBtn.classList.remove('hidden');
    });
  </script>

</body></html>