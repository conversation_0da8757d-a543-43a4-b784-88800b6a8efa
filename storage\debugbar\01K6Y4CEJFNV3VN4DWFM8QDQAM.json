{"__meta": {"id": "01K6Y4CEJFNV3VN4DWFM8QDQAM", "datetime": "2025-10-07 01:25:43", "utime": **********.121342, "method": "GET", "uri": "/employer/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.016238, "end": **********.12137, "duration": 4.105132102966309, "duration_str": "4.11s", "measures": [{"label": "Booting", "start": **********.016238, "relative_start": 0, "end": **********.18707, "relative_end": **********.18707, "duration": 0.*****************, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.187102, "relative_start": 0.*****************, "end": **********.121373, "relative_end": 2.86102294921875e-06, "duration": 3.****************, "duration_str": "3.93s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.214666, "relative_start": 0.*****************, "end": **********.216342, "relative_end": **********.216342, "duration": 0.0016760826110839844, "duration_str": "1.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.933951, "relative_start": 3.***************, "end": **********.118572, "relative_end": **********.118572, "duration": 0.*****************, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 5243408, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.32.5", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Employer/Dashboard", "param_count": null, "params": [], "start": **********.120835, "type": "react", "hash": "reactC:\\Users\\<USER>\\Desktop\\WorkWise\\resources\\js/Pages/Employer/Dashboard.jsxEmployer/Dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fresources%2Fjs%2FPages%2FEmployer%2FDashboard.jsx&line=1", "ajax": false, "filename": "Dashboard.jsx", "line": "?"}}]}, "queries": {"count": 152, "nb_statements": 151, "nb_visible_statements": 152, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.15536999999999995, "accumulated_duration_str": "155ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 51 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.262265, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "database.sqlite", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'KsZfqwJB6FXqqeqDnnzDwqTpHSWr6hZM8BAeIqHk' limit 1", "type": "query", "params": [], "bindings": ["KsZfqwJB6FXqqeqDnnzDwqTpHSWr6hZM8BAeIqHk"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.263227, "duration": 0.006, "duration_str": "6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database.sqlite", "explain": null, "start_percent": 0, "width_percent": 3.862}, {"sql": "select * from \"users\" where \"id\" = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.2938359, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database.sqlite", "explain": null, "start_percent": 3.862, "width_percent": 0.586}, {"sql": "select \"status\", \"created_at\" from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 101}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.04422, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:101", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=101", "ajax": false, "filename": "EmployerDashboardController.php", "line": "101"}, "connection": "database.sqlite", "explain": null, "start_percent": 4.447, "width_percent": 0.708}, {"sql": "select \"gig_jobs\".*, (select count(*) from \"bids\" where \"gig_jobs\".\"id\" = \"bids\".\"job_id\") as \"bids_count\" from \"gig_jobs\" where \"employer_id\" = 2 order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 113}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5695758, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:113", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=113", "ajax": false, "filename": "EmployerDashboardController.php", "line": "113"}, "connection": "database.sqlite", "explain": null, "start_percent": 5.155, "width_percent": 0.611}, {"sql": "select * from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5616322, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:137", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=137", "ajax": false, "filename": "EmployerDashboardController.php", "line": "137"}, "connection": "database.sqlite", "explain": null, "start_percent": 5.767, "width_percent": 0.656}, {"sql": "select * from \"gig_jobs\" where \"gig_jobs\".\"id\" in (1, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 61}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.574663, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:137", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=137", "ajax": false, "filename": "EmployerDashboardController.php", "line": "137"}, "connection": "database.sqlite", "explain": null, "start_percent": 6.423, "width_percent": 0.702}, {"sql": "select * from \"users\" where \"users\".\"id\" in (5, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 61}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5833821, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:137", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=137", "ajax": false, "filename": "EmployerDashboardController.php", "line": "137"}, "connection": "database.sqlite", "explain": null, "start_percent": 7.125, "width_percent": 0.515}, {"sql": "select * from \"projects\" where \"employer_id\" = 2 and \"contract_signed\" = 1 and \"status\" in ('active', 'in_progress') order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [2, 1, "active", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 162}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 64}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.599833, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "EmployerDashboardController.php:162", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=162", "ajax": false, "filename": "EmployerDashboardController.php", "line": "162"}, "connection": "database.sqlite", "explain": null, "start_percent": 7.64, "width_percent": 0.624}, {"sql": "select * from \"notifications\" where \"user_id\" = 2 and \"type\" in ('escrow_status', 'deadline_approaching', 'message_received') order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [2, "escrow_status", "deadline_approaching", "message_received"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 148}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 185}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.612687, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "NotificationManager.php:148", "source": {"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationManager.php&line=148", "ajax": false, "filename": "NotificationManager.php", "line": "148"}, "connection": "database.sqlite", "explain": null, "start_percent": 8.264, "width_percent": 0.792}, {"sql": "select * from \"notifications\" where \"user_id\" = 2 and \"type\" = 'escrow_status' and \"is_read\" = 0 order by \"created_at\" desc limit 3", "type": "query", "params": [], "bindings": [2, "escrow_status", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 176}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 186}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.622781, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "NotificationManager.php:176", "source": {"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationManager.php&line=176", "ajax": false, "filename": "NotificationManager.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 9.056, "width_percent": 0.708}, {"sql": "select * from \"contract_deadlines\" where exists (select * from \"projects\" where \"contract_deadlines\".\"contract_id\" = \"projects\".\"id\" and \"employer_id\" = 2) and \"due_date\" <= '2025-10-14' and \"due_date\" >= '2025-10-07' and \"status\" = 'pending' order by \"due_date\" asc limit 5", "type": "query", "params": [], "bindings": [2, "2025-10-14", "2025-10-07", "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 163}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 187}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.731316, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "NotificationManager.php:163", "source": {"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationManager.php&line=163", "ajax": false, "filename": "NotificationManager.php", "line": "163"}, "connection": "database.sqlite", "explain": null, "start_percent": 9.764, "width_percent": 2.375}, {"sql": "select * from \"notifications\" where \"user_id\" = 2 and \"type\" = 'message_received' and \"is_read\" = 0 order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [2, "message_received", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 189}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.7635581, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "NotificationManager.php:189", "source": {"index": 15, "namespace": null, "name": "app/Services/NotificationManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationManager.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationManager.php&line=189", "ajax": false, "filename": "NotificationManager.php", "line": "189"}, "connection": "database.sqlite", "explain": null, "start_percent": 12.139, "width_percent": 0.972}, {"sql": "select count(*) as aggregate from \"notifications\" where \"user_id\" = 2 and \"is_read\" = 0", "type": "query", "params": [], "bindings": [2, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/NotificationService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationService.php", "line": 36}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 189}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 67}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.779222, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "NotificationService.php:36", "source": {"index": 16, "namespace": null, "name": "app/Services/NotificationService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\NotificationService.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FNotificationService.php&line=36", "ajax": false, "filename": "NotificationService.php", "line": "36"}, "connection": "database.sqlite", "explain": null, "start_percent": 13.111, "width_percent": 0.605}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 24}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.788986, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:39", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=39", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "39"}, "connection": "database.sqlite", "explain": null, "start_percent": 13.716, "width_percent": 1.995}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and \"status\" = 'open'", "type": "query", "params": [], "bindings": [2, "open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 24}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.800879, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:41", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=41", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "41"}, "connection": "database.sqlite", "explain": null, "start_percent": 15.711, "width_percent": 0.451}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and \"created_at\" >= '2025-09-07 01:25:41'", "type": "query", "params": [], "bindings": [2, "2025-09-07 01:25:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 24}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.812812, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:43", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=43", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "43"}, "connection": "database.sqlite", "explain": null, "start_percent": 16.161, "width_percent": 0.541}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and \"status\" = 'completed'", "type": "query", "params": [], "bindings": [2, "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 50}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 24}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.823007, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:50", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=50", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "50"}, "connection": "database.sqlite", "explain": null, "start_percent": 16.702, "width_percent": 0.56}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2)", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 61}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.835367, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:61", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=61", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "61"}, "connection": "database.sqlite", "explain": null, "start_percent": 17.262, "width_percent": 0.579}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and \"created_at\" >= '2025-09-30 01:25:41'", "type": "query", "params": [], "bindings": [2, "2025-09-30 01:25:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 65}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.848156, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:65", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=65", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "65"}, "connection": "database.sqlite", "explain": null, "start_percent": 17.841, "width_percent": 0.463}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 67}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.859167, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:67", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=67", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "67"}, "connection": "database.sqlite", "explain": null, "start_percent": 18.305, "width_percent": 0.779}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 68}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8682408, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:68", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=68", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "68"}, "connection": "database.sqlite", "explain": null, "start_percent": 19.083, "width_percent": 0.489}, {"sql": "select * from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2)", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 200}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.881177, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:200", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=200", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "200"}, "connection": "database.sqlite", "explain": null, "start_percent": 19.573, "width_percent": 0.83}, {"sql": "select * from \"users\" where \"users\".\"id\" in (5, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 200}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 74}, {"index": 22, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 25}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.9033968, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:200", "source": {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=200", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "200"}, "connection": "database.sqlite", "explain": null, "start_percent": 20.403, "width_percent": 0.83}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 83}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.9189918, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:83", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=83", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "83"}, "connection": "database.sqlite", "explain": null, "start_percent": 21.233, "width_percent": 0.502}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and \"status\" in ('active', 'in_progress')", "type": "query", "params": [], "bindings": [2, "active", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.934107, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:85", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=85", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "85"}, "connection": "database.sqlite", "explain": null, "start_percent": 21.735, "width_percent": 0.438}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and \"status\" = 'completed'", "type": "query", "params": [], "bindings": [2, "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 87}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.948694, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:87", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=87", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "87"}, "connection": "database.sqlite", "explain": null, "start_percent": 22.173, "width_percent": 0.521}, {"sql": "select * from \"projects\" where \"employer_id\" = 2 and \"started_at\" is not null and \"completed_at\" is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 221}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 97}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.962835, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:221", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 221}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=221", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "221"}, "connection": "database.sqlite", "explain": null, "start_percent": 22.694, "width_percent": 0.766}, {"sql": "select sum(\"agreed_amount\") as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.980231, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:106", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=106", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "106"}, "connection": "database.sqlite", "explain": null, "start_percent": 23.46, "width_percent": 0.521}, {"sql": "select sum(\"agreed_amount\") as aggregate from \"projects\" where \"employer_id\" = 2 and \"created_at\" >= '2025-10-01 00:00:00'", "type": "query", "params": [], "bindings": [2, "2025-10-01 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 108}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.988928, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:108", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=108", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "108"}, "connection": "database.sqlite", "explain": null, "start_percent": 23.981, "width_percent": 1.757}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 109}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.0002692, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:109", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=109", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "109"}, "connection": "database.sqlite", "explain": null, "start_percent": 25.739, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 110}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.0119731, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:110", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=110", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "110"}, "connection": "database.sqlite", "explain": null, "start_percent": 26.144, "width_percent": 0.418}, {"sql": "select sum(\"agreed_amount\") as aggregate from \"projects\" where \"employer_id\" = 2 and \"created_at\" >= '2025-10-01 00:00:00'", "type": "query", "params": [], "bindings": [2, "2025-10-01 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 238}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 117}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.01978, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:238", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 238}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=238", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "238"}, "connection": "database.sqlite", "explain": null, "start_percent": 26.562, "width_percent": 0.457}, {"sql": "select sum(\"agreed_amount\") as aggregate from \"projects\" where \"employer_id\" = 2 and \"created_at\" between '2025-09-01 00:00:00' and '2025-09-30 23:59:59'", "type": "query", "params": [], "bindings": [2, "2025-09-01 00:00:00", "2025-09-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 244}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 117}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 27}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.032748, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:244", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=244", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "244"}, "connection": "database.sqlite", "explain": null, "start_percent": 27.019, "width_percent": 0.418}, {"sql": "select * from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and \"created_at\" >= '2025-09-07 01:25:42'", "type": "query", "params": [], "bindings": [2, "2025-09-07 01:25:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 129}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.049082, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:129", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=129", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "129"}, "connection": "database.sqlite", "explain": null, "start_percent": 27.438, "width_percent": 0.676}, {"sql": "select * from \"gig_jobs\" where \"gig_jobs\".\"id\" in (1, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 129}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 28}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.061937, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:129", "source": {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=129", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "129"}, "connection": "database.sqlite", "explain": null, "start_percent": 28.114, "width_percent": 0.521}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 153}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.070204, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:153", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=153", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "153"}, "connection": "database.sqlite", "explain": null, "start_percent": 28.635, "width_percent": 0.444}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and exists (select * from \"projects\" where \"gig_jobs\".\"id\" = \"projects\".\"job_id\")", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.082396, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:155", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 155}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=155", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "155"}, "connection": "database.sqlite", "explain": null, "start_percent": 29.079, "width_percent": 0.438}, {"sql": "select \"gig_jobs\".*, (select count(*) from \"bids\" where \"gig_jobs\".\"id\" = \"bids\".\"job_id\") as \"bids_count\" from \"gig_jobs\" where \"employer_id\" = 2 and exists (select * from \"projects\" where \"gig_jobs\".\"id\" = \"projects\".\"job_id\")", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 261}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 161}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.094404, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:261", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 261}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=261", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "261"}, "connection": "database.sqlite", "explain": null, "start_percent": 29.517, "width_percent": 0.599}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 83}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 275}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 162}, {"index": 19, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}], "start": **********.1035662, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:83", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=83", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "83"}, "connection": "database.sqlite", "explain": null, "start_percent": 30.115, "width_percent": 0.412}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and \"status\" in ('active', 'in_progress')", "type": "query", "params": [], "bindings": [2, "active", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 275}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 162}, {"index": 19, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}], "start": **********.114733, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:85", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=85", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "85"}, "connection": "database.sqlite", "explain": null, "start_percent": 30.527, "width_percent": 0.502}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and \"status\" = 'completed'", "type": "query", "params": [], "bindings": [2, "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 87}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 275}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 162}, {"index": 19, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}], "start": **********.125703, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:87", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=87", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "87"}, "connection": "database.sqlite", "explain": null, "start_percent": 31.029, "width_percent": 0.779}, {"sql": "select * from \"projects\" where \"employer_id\" = 2 and \"started_at\" is not null and \"completed_at\" is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 221}, {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 97}, {"index": 17, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 275}, {"index": 18, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 162}, {"index": 19, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 29}], "start": **********.134579, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:221", "source": {"index": 15, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 221}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=221", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "221"}, "connection": "database.sqlite", "explain": null, "start_percent": 31.808, "width_percent": 0.457}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-07' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.146048, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 32.265, "width_percent": 0.528}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-07' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.154155, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 32.793, "width_percent": 0.721}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-07' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.165326, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 33.514, "width_percent": 0.399}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-08' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.1737208, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 33.913, "width_percent": 0.824}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-08' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.185199, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 34.736, "width_percent": 0.65}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-08' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.1975422, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 35.386, "width_percent": 0.399}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-09' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.2045832, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 35.786, "width_percent": 0.399}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-09' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.2154539, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 36.185, "width_percent": 0.496}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-09' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.2251098, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 36.68, "width_percent": 0.489}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-10' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.233189, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 37.169, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-10' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.244054, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 37.575, "width_percent": 0.56}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-10' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.2523382, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 38.135, "width_percent": 0.412}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-11' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.2627509, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 38.547, "width_percent": 0.508}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-11' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.270188, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 39.055, "width_percent": 0.451}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-11' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.280668, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 39.506, "width_percent": 0.418}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-12' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.2877302, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 39.924, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-12' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.2989209, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 40.33, "width_percent": 0.496}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-12' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.310368, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 40.825, "width_percent": 0.521}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-13' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.3178172, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 41.346, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-13' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.331691, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 41.752, "width_percent": 0.798}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-13' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.345522, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 42.55, "width_percent": 0.476}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-14' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.3530412, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 43.026, "width_percent": 0.418}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-14' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.364604, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 43.445, "width_percent": 0.496}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-14' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.372346, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 43.94, "width_percent": 1.725}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-15' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.383366, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 45.665, "width_percent": 0.425}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-15' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.394273, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 46.09, "width_percent": 0.541}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-15' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.402103, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 46.631, "width_percent": 0.431}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-16' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.412707, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 47.062, "width_percent": 0.528}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-16' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.4202108, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 47.59, "width_percent": 0.508}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-16' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.431088, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 48.098, "width_percent": 0.418}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-17' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.438924, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 48.516, "width_percent": 1.57}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-17' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.450065, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 50.087, "width_percent": 0.508}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-17' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.460723, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 50.595, "width_percent": 0.463}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-18' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.4686608, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 51.059, "width_percent": 1.274}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-18' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.485139, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 52.333, "width_percent": 0.792}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-18' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-18"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.500561, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 53.125, "width_percent": 0.521}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-19' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.514221, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 53.646, "width_percent": 0.56}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-19' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.528447, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 54.206, "width_percent": 0.599}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-19' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.536326, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 54.805, "width_percent": 0.431}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-20' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.547189, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 55.236, "width_percent": 0.463}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-20' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.5554368, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 55.699, "width_percent": 1.828}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-20' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.565792, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 57.527, "width_percent": 0.412}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-21' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.575519, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 57.939, "width_percent": 0.785}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-21' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.583763, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 58.724, "width_percent": 0.47}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-21' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.595295, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 59.194, "width_percent": 0.573}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-22' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.610761, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 59.767, "width_percent": 0.702}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-22' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.619616, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 60.469, "width_percent": 0.47}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-22' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-22"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.630731, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 60.938, "width_percent": 0.438}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-23' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.6384268, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 61.376, "width_percent": 1.41}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-23' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.649025, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 62.786, "width_percent": 0.47}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-23' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-23"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.657755, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 63.255, "width_percent": 0.727}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-24' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.6663811, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 63.983, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-24' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.676184, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 64.388, "width_percent": 0.953}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-24' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-24"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.684407, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 65.341, "width_percent": 0.412}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-25' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.695069, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 65.753, "width_percent": 0.656}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = 2) and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-25' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.705216, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:179", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=179", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "179"}, "connection": "database.sqlite", "explain": null, "start_percent": 66.409, "width_percent": 1.673}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-25' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.715783, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=181", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "181"}, "connection": "database.sqlite", "explain": null, "start_percent": 68.083, "width_percent": 0.412}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = 2 and strftime('%Y-%m-%d', \"created_at\") = cast('2025-09-26' as text)", "type": "query", "params": [], "bindings": [2, "2025-09-26"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, {"index": 20, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/EmployerDashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\EmployerDashboardController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.7252882, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "EmployerAnalyticsService.php:176", "source": {"index": 16, "namespace": null, "name": "app/Services/EmployerAnalyticsService.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Services\\EmployerAnalyticsService.php", "line": 176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FServices%2FEmployerAnalyticsService.php&line=176", "ajax": false, "filename": "EmployerAnalyticsService.php", "line": "176"}, "connection": "database.sqlite", "explain": null, "start_percent": 68.495, "width_percent": 0.515}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.733686, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 69.009, "width_percent": 0.463}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7350051, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 69.473, "width_percent": 0.515}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736974, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 69.988, "width_percent": 0.605}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7428849, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 70.593, "width_percent": 1.384}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.746069, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 71.977, "width_percent": 0.656}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748148, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 72.633, "width_percent": 0.534}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7505, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 73.167, "width_percent": 0.656}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.752312, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 73.824, "width_percent": 0.47}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7537909, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 74.294, "width_percent": 0.496}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.755871, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 74.789, "width_percent": 1.68}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.759164, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 76.469, "width_percent": 0.869}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.761196, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 77.338, "width_percent": 0.483}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762978, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 77.821, "width_percent": 0.541}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7643402, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 78.361, "width_percent": 0.412}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765564, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 78.773, "width_percent": 0.418}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.76713, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 79.192, "width_percent": 0.489}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7683492, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 79.681, "width_percent": 0.399}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.769618, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 80.08, "width_percent": 0.534}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.771873, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 80.614, "width_percent": 0.476}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.77478, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 81.09, "width_percent": 0.708}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.777082, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 81.798, "width_percent": 0.483}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.778637, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 82.281, "width_percent": 0.476}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.779879, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 82.757, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.781072, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 83.163, "width_percent": 0.373}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.78239, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 83.536, "width_percent": 0.418}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.78349, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 83.954, "width_percent": 0.354}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.784533, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 84.308, "width_percent": 0.367}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.786057, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 84.675, "width_percent": 0.438}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.787153, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 85.113, "width_percent": 0.386}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.788368, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 85.499, "width_percent": 0.592}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.791936, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 86.091, "width_percent": 0.953}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.793998, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 87.044, "width_percent": 0.463}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.795516, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 87.507, "width_percent": 0.47}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" between ? and ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.796952, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 87.977, "width_percent": 0.431}, {"sql": "select \"gig_jobs\".*, (select count(*) from \"bids\" where \"gig_jobs\".\"id\" = \"bids\".\"job_id\") as \"bids_count\" from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" >= ? order by \"created_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.798742, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 88.408, "width_percent": 0.541}, {"sql": "select * from \"projects\" where \"employer_id\" = ? and \"contract_signed\" = ? and \"contract_signed_at\" >= ? order by \"contract_signed_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.802135, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 88.949, "width_percent": 0.541}, {"sql": "select * from \"projects\" where \"employer_id\" = ? and \"payment_released\" = ? and \"payment_released_at\" >= ? order by \"payment_released_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.803777, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 89.49, "width_percent": 0.508}, {"sql": "select * from \"contract_deadlines\" where exists (select * from \"projects\" where \"contract_deadlines\".\"contract_id\" = \"projects\".\"id\" and \"employer_id\" = ?) and \"status\" = ? and \"updated_at\" >= ? order by \"updated_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.807805, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 89.998, "width_percent": 0.663}, {"sql": "select * from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and \"created_at\" >= ? order by \"created_at\" desc limit 3", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.810493, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 90.661, "width_percent": 0.528}, {"sql": "select * from \"gig_jobs\" where \"gig_jobs\".\"id\" in (1, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8120759, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 91.189, "width_percent": 0.483}, {"sql": "select * from \"users\" where \"users\".\"id\" in (5, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.813619, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 91.671, "width_percent": 0.502}, {"sql": "select * from \"notifications\" where \"user_id\" = ? and \"created_at\" >= ? and \"type\" in (?, ?) order by \"created_at\" desc limit 2", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.817083, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 92.174, "width_percent": 0.496}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.912007, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 92.669, "width_percent": 0.656}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and strftime('%Y-%m-%d', \"created_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9168808, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 93.326, "width_percent": 0.599}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and strftime('%Y-%m-%d', \"contract_signed_at\") = cast(? as text)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.918511, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 93.924, "width_percent": 0.476}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9199631, "duration": 0.00522, "duration_str": "5.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 94.4, "width_percent": 3.36}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9270358, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 97.76, "width_percent": 0.605}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and \"contract_signed_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.928622, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 98.365, "width_percent": 0.476}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"employer_id\" = ? and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929856, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 98.841, "width_percent": 0.399}, {"sql": "select count(*) as aggregate from \"bids\" where exists (select * from \"gig_jobs\" where \"bids\".\"job_id\" = \"gig_jobs\".\"id\" and \"employer_id\" = ?) and \"created_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.931216, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 99.241, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from \"projects\" where \"employer_id\" = ? and \"contract_signed_at\" >= ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932244, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "database.sqlite", "explain": null, "start_percent": 99.646, "width_percent": 0.354}]}, "models": {"data": {"App\\Models\\GigJob": {"retrieved": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FGigJob.php&line=1", "ajax": false, "filename": "GigJob.php", "line": "?"}}, "App\\Models\\Bid": {"retrieved": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FBid.php&line=1", "ajax": false, "filename": "Bid.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 32, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 32}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/employer/dashboard", "action_name": "employer.dashboard", "controller_action": "App\\Http\\Controllers\\EmployerDashboardController@index", "uri": "GET employer/dashboard", "controller": "App\\Http\\Controllers\\EmployerDashboardController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=48\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FEmployerDashboardController.php&line=48\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/EmployerDashboardController.php:48-95</a>", "middleware": "web, auth, verified", "duration": "4.24s", "peak_memory": "6MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-949439507 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-949439507\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2039567870 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2039567870\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-885609489 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IllTQi9HVXFqQitla3UyMjIrYmtkWHc9PSIsInZhbHVlIjoiNnlhbnBDMlRNNFFESGJwWVoxODFFbGFXME5ySUMzdHJoQnpzUGF3MHhraHZtLzN2YndSbzh2SWlFQ2pkaG02YlZ4eGh2OE5JVERGbWdDZzdBcjJlQkdkODNVZmU2TmJtMXhFZXl2ZUNybnFnb21PMGZhd21Oek5ONllLOVdyekYiLCJtYWMiOiJmODY5ZDU0YjUxNDJlZDk4MjQyYTJiMGVjNTNhZWQwMjVlMDI4YTA0MmMwZDA0MTBhZWFiZDMyNGYxNjdjMzQxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6ImR1SXRWQWpvYTgxN1ZzenpQZFR3b0E9PSIsInZhbHVlIjoibmwxck1nM1pDSFl6WEZyZ1ZON05RZmFoc2dlaVlHc0lkdEd2cHRVZGtPYmxpang0cGM0aGlORjhoTHBLekVGYXdDTm1DWmJFNVpwWVhRYXcwTmFuM0ZsSUlDR1ZsT0pmaldzMDFqNzZkQ0E1MlIvSDljcUNUbXBOaW54OG5Qc1AiLCJtYWMiOiJkOWFlMWNlNzhiNGZkYWUyNjQ2ZTQyYTRjMGU4NjQ4MWE0OWU2ZGY4MGY1OGY3NGZmOWNjM2U2MjljNDJhOWQ4IiwidGFnIjoiIn0%3D; workwise_session=eyJpdiI6IkxtUHF1Z1FQZ09zMjVORWsrMmdvRmc9PSIsInZhbHVlIjoiNG1wS1J3NjVoLzM4RHdCRnZtUkpsMHhTNmw4TjF6VjJsRzNMUGlCa1BuTnVUaVZvVjhCTkNvR0pMMitDSUdRS2IxZld1aG1YWm9VOVg2aTkwc0l5ZVdXMTllYVRJUVpsTU0rbnRvU1ZVbjlyNmZ6Ti9HUWN0MWFCVkJFTnFhVmYiLCJtYWMiOiIzZTg2MzhiNDI4MDY0MzRhYzcwZmRkNDI2ZDdjMWViYTQ3YmZiNjUwNzU4NWExOTdlZDUwOTlhMjQzMDAzMzE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885609489\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1989427224 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">V8CEQ6BjfyNFplbxjZlRZR18PnQaTPTwpQRrp0dO</span>\"\n  \"<span class=sf-dump-key>workwise_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KsZfqwJB6FXqqeqDnnzDwqTpHSWr6hZM8BAeIqHk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989427224\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1690953507 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 07 Oct 2025 01:25:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1690953507\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1888383063 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">V8CEQ6BjfyNFplbxjZlRZR18PnQaTPTwpQRrp0dO</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K6Y4C9XHCHKCHF49DBAM7SS0</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>01K6Y4CAEPVK8D2KNATHB1WYQQ</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888383063\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/employer/dashboard", "action_name": "employer.dashboard", "controller_action": "App\\Http\\Controllers\\EmployerDashboardController@index"}, "badge": null}}