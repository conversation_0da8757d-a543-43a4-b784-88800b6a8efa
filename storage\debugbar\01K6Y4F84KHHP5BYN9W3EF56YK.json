{"__meta": {"id": "01K6Y4F84KHHP5BYN9W3EF56YK", "datetime": "2025-10-07 01:27:14", "utime": **********.84485, "method": "GET", "uri": "/jobs?search=&category=&budget_range=&experience_level=", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.305885, "end": **********.844934, "duration": 0.5390489101409912, "duration_str": "539ms", "measures": [{"label": "Booting", "start": **********.305885, "relative_start": 0, "end": **********.422461, "relative_end": **********.422461, "duration": 0.*****************, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.422498, "relative_start": 0.*****************, "end": **********.844941, "relative_end": 6.9141387939453125e-06, "duration": 0.****************, "duration_str": "422ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.434603, "relative_start": 0.*****************, "end": **********.436644, "relative_end": **********.436644, "duration": 0.0020411014556884766, "duration_str": "2.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.686237, "relative_start": 0.****************, "end": **********.834857, "relative_end": **********.834857, "duration": 0.****************, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 3833104, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.32.5", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Jobs/Index", "param_count": null, "params": [], "start": **********.844276, "type": "react", "hash": "reactC:\\Users\\<USER>\\Desktop\\WorkWise\\resources\\js/Pages/Jobs/Index.jsxJobs/Index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fresources%2Fjs%2FPages%2FJobs%2FIndex.jsx&line=1", "ajax": false, "filename": "Index.jsx", "line": "?"}}]}, "queries": {"count": 11, "nb_statements": 10, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.027709999999999995, "accumulated_duration_str": "27.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.467939, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "database.sqlite", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'kRhC8TRlZ3GL8ds8wcdeHAOqA3UDEPJXc7tacK3D' limit 1", "type": "query", "params": [], "bindings": ["kRhC8TRlZ3GL8ds8wcdeHAOqA3UDEPJXc7tacK3D"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.468902, "duration": 0.01507, "duration_str": "15.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database.sqlite", "explain": null, "start_percent": 0, "width_percent": 54.385}, {"sql": "select * from \"users\" where \"id\" = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 56}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 35}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 89}], "start": **********.5056372, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database.sqlite", "explain": null, "start_percent": 54.385, "width_percent": 5.521}, {"sql": "select count(*) as aggregate from \"gig_jobs\" where \"status\" = 'open'", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5291529, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "GigJobController.php:59", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=59", "ajax": false, "filename": "GigJobController.php", "line": "59"}, "connection": "database.sqlite", "explain": null, "start_percent": 59.906, "width_percent": 4.728}, {"sql": "select * from \"gig_jobs\" where \"status\" = 'open' order by \"created_at\" desc limit 12 offset 0", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5462801, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "GigJobController.php:59", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=59", "ajax": false, "filename": "GigJobController.php", "line": "59"}, "connection": "database.sqlite", "explain": null, "start_percent": 64.634, "width_percent": 6.135}, {"sql": "select * from \"users\" where \"users\".\"id\" in (2, 3, 4, 9)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.564806, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "GigJobController.php:59", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=59", "ajax": false, "filename": "GigJobController.php", "line": "59"}, "connection": "database.sqlite", "explain": null, "start_percent": 70.769, "width_percent": 5.558}, {"sql": "select * from \"bids\" where \"bids\".\"job_id\" in (2, 3, 7, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5896192, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "GigJobController.php:59", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=59", "ajax": false, "filename": "GigJobController.php", "line": "59"}, "connection": "database.sqlite", "explain": null, "start_percent": 76.326, "width_percent": 8.733}, {"sql": "select count(*) as aggregate from \"bids\" where \"bids\".\"job_id\" = 25 and \"bids\".\"job_id\" is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 64}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 62}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.6174731, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "GigJobController.php:64", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=64", "ajax": false, "filename": "GigJobController.php", "line": "64"}, "connection": "database.sqlite", "explain": null, "start_percent": 85.06, "width_percent": 3.537}, {"sql": "select count(*) as aggregate from \"bids\" where \"bids\".\"job_id\" = 7 and \"bids\".\"job_id\" is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 64}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 62}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.6344419, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "GigJobController.php:64", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=64", "ajax": false, "filename": "GigJobController.php", "line": "64"}, "connection": "database.sqlite", "explain": null, "start_percent": 88.596, "width_percent": 3.97}, {"sql": "select count(*) as aggregate from \"bids\" where \"bids\".\"job_id\" = 2 and \"bids\".\"job_id\" is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 64}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 62}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.648781, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "GigJobController.php:64", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=64", "ajax": false, "filename": "GigJobController.php", "line": "64"}, "connection": "database.sqlite", "explain": null, "start_percent": 92.566, "width_percent": 3.501}, {"sql": "select count(*) as aggregate from \"bids\" where \"bids\".\"job_id\" = 3 and \"bids\".\"job_id\" is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 64}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 62}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.664824, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "GigJobController.php:64", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/GigJobController.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Controllers\\GigJobController.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=64", "ajax": false, "filename": "GigJobController.php", "line": "64"}, "connection": "database.sqlite", "explain": null, "start_percent": 96.066, "width_percent": 3.934}]}, "models": {"data": {"App\\Models\\Bid": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FBid.php&line=1", "ajax": false, "filename": "Bid.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\GigJob": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FGigJob.php&line=1", "ajax": false, "filename": "GigJob.php", "line": "?"}}}, "count": 15, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 15}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/jobs?budget_range=&category=&experience_level=&search=", "action_name": "jobs.index", "controller_action": "App\\Http\\Controllers\\GigJobController@index", "uri": "GET jobs", "controller": "App\\Http\\Controllers\\GigJobController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FGigJobController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/GigJobController.php:15-72</a>", "middleware": "web, auth.redirect", "duration": "570ms", "peak_memory": "4MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-847011692 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>budget_range</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>experience_level</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-847011692\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-390602969 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-390602969\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1637023088 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlFJVjhWaDdpMXAyWVJRcU11cG9YeVE9PSIsInZhbHVlIjoidW04OFNFdHUzM2NaMWdzSC9WNFZXTG9LMklrRG8yeUJRNFF6cUJiZXJtV2dLckt5d3ZDR2ZURXNxUXFYcGNIaTBqOTRWVkdMaUEya3phT1hnVjdEbG5UNXZnV1Q3MksxSkpGSVgvcEsybzdtWXBoRzZYOUhzd01SWXN3STVaZmwiLCJtYWMiOiJmNGMxNTkwNGIzYmM4NjA5M2ZjY2FjMTk2ZDlhODQzMjAwNTY0MjIwMWQwM2JmM2QyYWU2OWYwYmZlZjFjMmI0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/jobs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,es;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IlFJVjhWaDdpMXAyWVJRcU11cG9YeVE9PSIsInZhbHVlIjoidW04OFNFdHUzM2NaMWdzSC9WNFZXTG9LMklrRG8yeUJRNFF6cUJiZXJtV2dLckt5d3ZDR2ZURXNxUXFYcGNIaTBqOTRWVkdMaUEya3phT1hnVjdEbG5UNXZnV1Q3MksxSkpGSVgvcEsybzdtWXBoRzZYOUhzd01SWXN3STVaZmwiLCJtYWMiOiJmNGMxNTkwNGIzYmM4NjA5M2ZjY2FjMTk2ZDlhODQzMjAwNTY0MjIwMWQwM2JmM2QyYWU2OWYwYmZlZjFjMmI0IiwidGFnIjoiIn0%3D; workwise_session=eyJpdiI6ImFzc0RPQ3MvNW0wK0N2VzZIcVQ0WWc9PSIsInZhbHVlIjoiSisxc3paMWs4Z3UvbFl6V09QSC9BTGkxTGJJN1UwUkJYaWl0c082WE55RFFQcHpiejRVclltWHJZWUFmMTRJYVVVNUFSVmdRQ2ZFVXduVlIzSkp2Z0hRTU53eUFLNlpaWGQ5K2dzRm9RcUFqTHhRZG0wRVJqRG1NNXZiUTczR0oiLCJtYWMiOiIxYTA3MWQwZGQ1MjM2ZjE5MmMyZmI1OThmMDJhMWI5MDc1ZGRiYWViMTViNTYzMDMzZGRiZjYzMWM0ODM5MDAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637023088\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-577452236 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5E2KvGDRQdZxvf3kthvKUShTQCvzWsHz4NYJ5kJ</span>\"\n  \"<span class=sf-dump-key>workwise_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kRhC8TRlZ3GL8ds8wcdeHAOqA3UDEPJXc7tacK3D</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577452236\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1044692990 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 07 Oct 2025 01:27:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044692990\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-745925895 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E5E2KvGDRQdZxvf3kthvKUShTQCvzWsHz4NYJ5kJ</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K6Y4F1Y0TQJKK2RGFHRNHKG9</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>01K6Y4F29236GM14CDN4WFY73B</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745925895\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/jobs?budget_range=&category=&experience_level=&search=", "action_name": "jobs.index", "controller_action": "App\\Http\\Controllers\\GigJobController@index"}, "badge": null}}