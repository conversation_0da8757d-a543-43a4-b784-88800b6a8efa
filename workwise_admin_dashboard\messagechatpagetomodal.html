<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Chat</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<style>
        body {
            font-family: 'Manrope', sans-serif;
        }
    </style>
</head>
<body class="bg-[#F8F9FA]">
<div class="relative flex h-screen w-full group/design-root overflow-hidden">
<div class="flex-1 bg-white border-r border-gray-200">
<div class="p-4 border-b border-gray-200">
<h2 class="text-xl font-bold">Messages</h2>
</div>
<div class="overflow-y-auto">
<div class="flex items-center gap-4 p-4 border-b border-gray-200 bg-blue-50 cursor-pointer">
<div class="relative">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-12 w-12" data-alt="John Doe's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDt1i9sut1CqT13M_WeiTgWds1bcPjcyp_1o8DjJgqxyqg-1m9oybg2RTmd3cssYFRVLxEecL4-f-ZGdzFPmpldTYgtrlPn2hmYFP-L-R_WzVLdb8iP_lrWiANZ8djQH6YWVarxZDegJcTPifjVC5dweUh1uKStjJvj-OdtjF7sBmbUA7OXtZtQrdrih9HjIkP_OxPIMfd7ZqbF1QMxCUfCcasoOZw6azz28MDl0IDB-__9AHG2DZK37tmKgEN7fhm15Zx4nSOGwQ");'></div>
<div class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
</div>
<div class="flex-1">
<p class="text-[#0e1a13] text-base font-semibold leading-normal">John Doe</p>
<p class="text-[#6C757D] text-sm truncate">Perfect, that's much clearer...</p>
</div>
<div class="text-right">
<p class="text-xs text-[#6C757D]">10:36 AM</p>
<span class="inline-block bg-[#007BFF] text-white text-xs font-bold rounded-full px-2 py-1 mt-1">1</span>
</div>
</div>
<div class="flex items-center gap-4 p-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-12 w-12" data-alt="Jane Smith's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCYwemxDwc3WgeoUOYjcx6nNfV6An_UsGg-WMaUJn85StuOoqTgrYbrliac8uQ-JT4yATP_SyzljpkLbpQxYQ2MSLCd_vhTL_b0gzehmSreTl59hc_3fug0F7d_K0VQw6-7jQQqbjZm-sYgytebZmQ5spbK3Yfs0u_mHAVeCDrs9UUdAqmRWb9NC1FqhBIpartOPVuX2D2fPQK4Jc9n1tkVsrQ80jv6XPeva38pROaOWMfNfE4d5JnwIiaOSia58aWxtb0SDHhn2g");'></div>
<div class="flex-1">
<p class="text-[#0e1a13] text-base font-semibold leading-normal">Jane Smith</p>
<p class="text-[#6C757D] text-sm truncate">See you then!</p>
</div>
<div class="text-right">
<p class="text-xs text-[#6C757D]">Yesterday</p>
</div>
</div>
<div class="flex items-center gap-4 p-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-12 w-12" data-alt="Mike Johnson's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBXR_M6zuXMmVlqR3-VhJQPAUGlEyOncCb9pQh8103ySBGaxXGivqOFsAEUErtCvavntXzr4X0koOCTymgd_tG8cTmORI2pQDCR2y25UkktDacKx6ealEM9LIOLJPiUDx-beKeqUTda8Jv2RuBy2SgLgVM5Glz6SK5Y6PxoF9etGN3bsBIs-WB9AMk1lleD3olz5n4ZSdfV4ZLDRUvpQ0Sb5k_i1_Sg7NhYkliSvzHBMaQVmG3rhjIDPS3ex0Q2Mmn5aRFLETTkgg");'></div>
<div class="flex-1">
<p class="text-[#0e1a13] text-base font-semibold leading-normal">Mike Johnson</p>
<p class="text-[#6C757D] text-sm truncate">Let's discuss the project details.</p>
</div>
<div class="text-right">
<p class="text-xs text-[#6C757D]">2 days ago</p>
</div>
</div>
</div>
</div>
<div class="flex flex-col h-full w-2/3">
<header class="flex items-center gap-4 bg-white px-6 py-4 border-b border-[#E9F5FF]">
<div class="relative">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-12 w-12" data-alt="John Doe's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDt1i9sut1CqT13M_WeiTgWds1bcPjcyp_1o8DjJgqxyqg-1m9oybg2RTmd3cssYFRVLxEecL4-f-ZGdzFPmpldTYgtrlPn2hmYFP-L-R_WzVLdb8iP_lrWiANZ8djQH6YWVarxZDegJcTPifjVC5dweUh1uKStjJvj-OdtjF7sBmbUA7OXtZtQrdrih9HjIkP_OxPIMfd7ZqbF1QMxCUfCcasoOZw6azz28MDl0IDB-__9AHG2DZK37tmKgEN7fhm15Zx4nSOGwQ");'></div>
<div class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
</div>
<div class="flex-1">
<p class="text-[#0e1a13] text-lg font-semibold leading-normal">John Doe</p>
<p class="text-[#6C757D] text-sm">Online</p>
</div>
<button class="text-[#6C757D] flex items-center justify-center p-2 rounded-full hover:bg-gray-100">
<span class="material-symbols-outlined">more_horiz</span>
</button>
</header>
<main class="flex-1 overflow-y-auto p-6 space-y-6">
<div class="text-center my-4">
<span class="text-xs text-[#6C757D] bg-[#F8F9FA] px-2">Today</span>
</div>
<div class="flex items-end gap-3 p-4">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" data-alt="John Doe's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCpT8nY21HzlAbmtjAGZQHzoKKZRCrFKv2Xej9k6OxFlDVoU5hkQAtUMNHni8E48elM1BJQZ4dk5oa8xGGi9nfKMPbIQD0pyFYvAZPJ8BLlTNemcT-zBOMguwz4A8CUx9PEClP5zkNwsavl93k0h_CTX9xMeStUjOaoNTxKtkajOeYqJNGzkG7UtGi8O7r9E9WzCWViZOGQ6M1ypB966uG8G-5SyPimij46zeXylPvCoN4siGV1WUoxFhsdBvBlHDZA-ndFKyY5SA");'></div>
<div class="flex flex-1 flex-col gap-1 items-start">
<div class="flex items-baseline gap-2">
<p class="text-[#0e1a13] text-[13px] font-medium leading-normal">John Doe</p>
<p class="text-[#6C757D] text-xs">10:30 AM</p>
</div>
<p class="text-base font-normal leading-normal flex max-w-[420px] rounded-lg px-4 py-3 bg-white text-[#0e1a13] rounded-tl-none shadow-sm">Hi, I have a question about the project. I'm reviewing the design brief and I need some clarification on the target audience.</p>
</div>
</div>
<div class="flex items-end gap-3 p-4 justify-end">
<div class="flex flex-1 flex-col gap-1 items-end">
<div class="flex items-baseline gap-2">
<p class="text-[#0e1a13] text-[13px] font-medium leading-normal text-right">You</p>
<p class="text-[#6C757D] text-xs">10:32 AM</p>
</div>
<p class="text-base font-normal leading-normal flex max-w-[420px] rounded-lg px-4 py-3 bg-[#E9F5FF] text-[#0e1a13] rounded-tr-none shadow-sm">Sure, what is it?</p>
</div>
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" data-alt="Your profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDok-HTiuaExigZpPqZCicgEBLv0HV2IlCG9uZjC0496JYdIEhYs2UFgqV5Ch7p4jmX_Lq8WE603_nh_vl_XPgecxaQrA9a_wQWUapIpnx6X058Wxc6-lJAsdVjD-sF8ZCQ_9FL5OBSiRqaiWHAZhLup2P890B6oHdLq8LDDCoB2YreUpBj6NSTKqApYT-DXbGOIj7hrPHUIZ56WtE7T61em6LYVOUmi1dD4Tim78Ay5ty_i-G2DTFiNV7j7FWIQepRDC44TIbRiQ");'></div>
</div>
<div class="flex items-end gap-3 p-4">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" data-alt="John Doe's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuALsfaejldew_6HD3Y9cnnq0Aeibr6wnyJGUKBcgosB0bbIUxzq5v2rU1x9pYBkiVx9-JCOF-as0KBGBRzeWRmN9o2LxN9VzZ0pTN_LecHCOey1p1Kz2s0QgNDdYL6-9ox-GczKMK6lFZxwYE8Gbl6O0Zz2rhw6T0Ow8ZnhZyl0ercSM8oibxGWOpv5Ol6W-Mp_lFKjlHh41wJaM3Ducp27RLFto9HPQ7eD06jt2Z-CjMwMAy1DHBSmp1Ng1qOe7uYyAzOG6ov7Uw");'></div>
<div class="flex flex-1 flex-col gap-1 items-start">
<div class="flex items-baseline gap-2">
<p class="text-[#0e1a13] text-[13px] font-medium leading-normal">John Doe</p>
<p class="text-[#6C757D] text-xs">10:33 AM</p>
</div>
<p class="text-base font-normal leading-normal flex max-w-[420px] rounded-lg px-4 py-3 bg-white text-[#0e1a13] rounded-tl-none shadow-sm">The brief mentions "young professionals," but that's quite broad. Could we narrow it down to a specific age range or industry?</p>
</div>
</div>
<div class="flex items-end gap-3 p-4 justify-end">
<div class="flex flex-1 flex-col gap-1 items-end">
<div class="flex items-baseline gap-2">
<p class="text-[#0e1a13] text-[13px] font-medium leading-normal text-right">You</p>
<p class="text-[#6C757D] text-xs">10:35 AM</p>
</div>
<p class="text-base font-normal leading-normal flex max-w-[420px] rounded-lg px-4 py-3 bg-[#E9F5FF] text-[#0e1a13] rounded-tr-none shadow-sm">Good question. Let's target tech professionals aged 25-35. That should give us a more focused scope.</p>
</div>
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" data-alt="Your profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCc7h5vK-1H1vy4ySecmW5e5dioVghHZJFvKJ0-1NvSquxhDJoizV5IukhduC8a61css6UtCRzdbXuQEt7LSRgQO5cU_vCfY-i97XrUpxM_Ql6cAoSSD0Nsn7Cd-MkGBWz4M6J8jrHwZ19wx_1GENx-TS-fahOqDEfIgiYHYbDFFQv24vmBLmdL6Sw6EjSYCqU4U7WW_-B3xPJYxXocOPHou5nuvTRcsYg0Zu76SYfaDd7O4fCQiNPS8FbkWi2z2K5V_zGlLNW2bw");'></div>
</div>
<div class="flex items-end gap-3 p-4">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" data-alt="John Doe's profile picture." style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDnZv4WAQhb9y-vbC1sXVy-L259S6udc9qYk4vBpjMGAPk9l_pwBOP9cXHImG1c6ZgWJ3WDvjF3MTg2EQizHU0HoesQMhmvja34X4NCiyF51hrGELnXiCA3zPfTBDWLMk7GD2FlODX_5JrSgTym0qaPTui4n1xnyx0lYRh5Ye_L2_ouhseyASHlFT3WB6OoDMpAV7j3Go4-cEl79i8R2iAetyCMHi5a96KWtyADUIxInyy-qnn5qgu65D18JE9W6khrFMRmCUY61Q");'></div>
<div class="flex flex-1 flex-col gap-1 items-start">
<div class="flex items-baseline gap-2">
<p class="text-[#0e1a13] text-[13px] font-medium leading-normal">John Doe</p>
<p class="text-[#6C757D] text-xs">10:36 AM</p>
</div>
<div class="flex flex-col gap-2">
<p class="text-base font-normal leading-normal flex max-w-[420px] rounded-lg px-4 py-3 bg-white text-[#0e1a13] rounded-tl-none shadow-sm">Perfect, that's much clearer. I'll get started on the initial concepts. Also, I've attached the project timeline for your review.</p>
<div class="flex items-center gap-2 p-3 rounded-lg border border-gray-200 bg-white max-w-[300px]">
<span class="material-symbols-outlined text-[#007BFF]">description</span>
<div class="flex-1">
<p class="text-sm font-medium text-[#0e1a13]">Project_Timeline.pdf</p>
<p class="text-xs text-[#6C757D]">1.2 MB</p>
</div>
<button class="p-2 text-[#6C757D] hover:text-[#007BFF]">
<span class="material-symbols-outlined">download</span>
</button>
</div>
</div>
</div>
</div>
</main>
<footer class="bg-white p-4 border-t border-[#E9F5FF] @container">
<div class="flex gap-3 pb-3 overflow-x-auto">
<button class="flex h-9 shrink-0 items-center justify-center gap-x-2 rounded-full bg-gray-100 hover:bg-gray-200 pl-4 pr-4 transition-colors">
<p class="text-[#0e1a13] text-sm font-medium leading-normal">Send an Offer</p>
</button>
<button class="flex h-9 shrink-0 items-center justify-center gap-x-2 rounded-full bg-gray-100 hover:bg-gray-200 pl-4 pr-4 transition-colors">
<p class="text-[#0e1a13] text-sm font-medium leading-normal">Request Payment</p>
</button>
<button class="flex h-9 shrink-0 items-center justify-center gap-x-2 rounded-full bg-gray-100 hover:bg-gray-200 pl-4 pr-4 transition-colors">
<p class="text-[#0e1a13] text-sm font-medium leading-normal">Schedule a Meeting</p>
</button>
</div>
<div class="flex items-center gap-4">
<label class="flex flex-col min-w-40 h-12 flex-1">
<div class="flex w-full flex-1 items-stretch rounded-lg h-full">
<input class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#0e1a13] focus:outline-0 focus:ring-1 focus:ring-[#007BFF] border-gray-200 bg-white h-full placeholder:text-[#6C757D] px-4 text-base font-normal leading-normal" placeholder="Type your message..." value=""/>
<div class="flex items-center justify-center pr-2">
<button class="flex items-center justify-center p-2 text-[#6C757D] hover:text-[#007BFF] rounded-full">
<span class="material-symbols-outlined">attachment</span>
</button>
</div>
</div>
</label>
<button class="min-w-[96px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-6 bg-[#007BFF] hover:bg-blue-600 text-white text-base font-medium leading-normal transition-colors flex gap-2">
<span class="truncate">Send</span>
<span class="material-symbols-outlined">send</span>
</button>
</div>
</footer>
</div>
</div>

</body></html>