<!DOCTYPE html>
<html class="light" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Freelance Contract</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&amp;display=swap" rel="stylesheet"/>
<style>
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .no-print {
                display: none;
            }
            .page-break {
                page-break-before: always;
            }
        }
    </style>
<script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    fontFamily: {
                        "body": ["Roboto", "sans-serif"],
                        "display": ["Inter", "sans-serif"]
                    },
                },
            },
        }
    </script>
</head>
<body class="bg-gray-200 font-body text-gray-900">
<div class="flex flex-col items-center py-10">
<div class="w-full max-w-4xl">
<div class="bg-white shadow-lg p-16">
<header class="flex justify-between items-center pb-8 border-b border-gray-300">
<div class="flex items-center gap-4">
<svg class="h-10 w-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path></svg>
<span class="text-2xl font-bold text-gray-800">WorkWise</span>
</div>
<div class="text-right">
<h1 class="text-3xl font-display font-bold">Freelance Contract</h1>
<p class="text-sm text-gray-500 mt-1">Contract ID: #123456</p>
</div>
</header>
<main class="mt-10">
<section class="mb-10">
<h2 class="text-xl font-semibold mb-4 pb-2 border-b">Parties Involved</h2>
<div class="grid grid-cols-2 gap-12 text-base">
<div>
<p class="font-semibold text-gray-800">Employer</p>
<p>John Smith</p>
<p>Acme Corporation</p>
</div>
<div>
<p class="font-semibold text-gray-800">Gig Worker</p>
<p>Jane Doe</p>
<p>Freelance Designer</p>
</div>
</div>
</section>
<section class="mb-10">
<h2 class="text-xl font-semibold mb-4 pb-2 border-b">Scope of Work</h2>
<div class="space-y-4 text-base leading-relaxed">
<div>
<p class="font-semibold">Project Title:</p>
<p>UI/UX Design for Mobile App</p>
</div>
<div>
<p class="font-semibold">Project Description:</p>
<p>The project scope includes the full UI/UX design for a new mobile application. This includes wireframes, mockups, and prototypes for all major screens and user flows. The final deliverables should be provided in Figma format.</p>
</div>
<div>
<p class="font-semibold">Timeline:</p>
<p>Start Date: October 26, 2023 - End Date: December 31, 2023</p>
</div>
</div>
</section>
<section class="mb-10">
<h2 class="text-xl font-semibold mb-4 pb-2 border-b">Payment Terms</h2>
<div class="space-y-4 text-base leading-relaxed">
<div>
<p class="font-semibold">Total Project Cost:</p>
<p class="text-lg font-bold">$5,000</p>
</div>
<div>
<p class="font-semibold">Payment Schedule:</p>
<p>Payment of $5,000 will be made in two installments: 50% upfront, and 50% upon project completion and delivery of all assets. Invoices should be submitted to the accounts payable department.</p>
</div>
</div>
</section>
<section class="mb-10">
<h2 class="text-xl font-semibold mb-4 pb-2 border-b">Responsibilities</h2>
<div class="space-y-2 text-base leading-relaxed">
<p><span class="font-semibold">Gig Worker:</span> Responsible for delivering the designs as per the project scope.</p>
<p><span class="font-semibold">Employer:</span> Responsible for providing timely feedback and access to necessary brand assets.</p>
</div>
</section>
<section class="mb-10">
<h2 class="text-xl font-semibold mb-4 pb-2 border-b">Communication</h2>
<p class="text-base leading-relaxed">Regular check-ins will be held twice a week via video call. All project-related communication will be managed through the WorkWise platform's messaging system to ensure a clear record.</p>
</section>
<section class="mb-10">
<h2 class="text-xl font-semibold mb-4 pb-2 border-b">Dispute Resolution</h2>
<p class="text-base leading-relaxed">Any disputes arising from this contract will be resolved through the WorkWise Dispute Resolution Center. Both parties agree to abide by the platform's mediation process.</p>
</section>
<section>
<h2 class="text-xl font-semibold mb-4 pb-2 border-b">Additional Terms</h2>
<p class="text-base leading-relaxed">Confidentiality of all project materials is required. The Gig Worker retains the right to use the completed project in their portfolio, with the employer's permission.</p>
</section>
</main>
</div>
<div class="bg-white shadow-lg p-16 mt-4 page-break">
<header class="text-center pb-8 border-b">
<h2 class="text-2xl font-display font-bold">Signature Page</h2>
</header>
<div class="mt-20">
<p class="text-center text-base leading-relaxed mb-16">By signing below, both parties agree to the terms and conditions outlined in this freelance contract.</p>
<div class="grid grid-cols-2 gap-24">
<div class="text-center">
<div class="border-b-2 border-gray-400 pb-12"></div>
<p class="mt-4 font-semibold">John Smith</p>
<p class="text-sm text-gray-600">Employer, Acme Corporation</p>
<p class="text-sm text-gray-500 mt-2">Date: ____________________</p>
</div>
<div class="text-center">
<div class="border-b-2 border-gray-400 pb-12"></div>
<p class="mt-4 font-semibold">Jane Doe</p>
<p class="text-sm text-gray-600">Gig Worker, Freelance Designer</p>
<p class="text-sm text-gray-500 mt-2">Date: ____________________</p>
</div>
</div>
</div>
<footer class="mt-32 text-center text-xs text-gray-500 border-t pt-6">
<p>© 2023 WorkWise. All Rights Reserved. This is a legally binding document.</p>
</footer>
</div>
</div>
<div class="no-print mt-8 flex justify-center gap-4">
<button class="flex items-center justify-center rounded-lg bg-blue-600 px-6 py-2 text-white font-semibold hover:bg-blue-700 transition-colors" onclick="window.print()">
<svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<path d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
            Print / Download
        </button>
</div>
</div>

</body></html>