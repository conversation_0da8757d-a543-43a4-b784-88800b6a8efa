{"__meta": {"id": "01K6Y4C9XHCHKCHF49DBAM7SS0", "datetime": "2025-10-07 01:25:38", "utime": **********.353949, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.044904, "end": **********.353975, "duration": 9.309071063995361, "duration_str": "9.31s", "measures": [{"label": "Booting", "start": **********.044904, "relative_start": 0, "end": **********.546629, "relative_end": **********.546629, "duration": 0.****************, "duration_str": "502ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.546742, "relative_start": 0.***************, "end": **********.353979, "relative_end": 4.0531158447265625e-06, "duration": 8.***************, "duration_str": "8.81s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.604586, "relative_start": 0.****************, "end": **********.616201, "relative_end": **********.616201, "duration": 0.*****************, "duration_str": "11.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.343592, "relative_start": 9.***************, "end": **********.345899, "relative_end": **********.345899, "duration": 0.0023071765899658203, "duration_str": "2.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 4645216, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.32.5", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 7, "nb_statements": 6, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.034640000000000004, "accumulated_duration_str": "34.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.670944, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "database.sqlite", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'uwihuBM9XZ0NysnvoeBDaQizVKRVklkJACdwi3Kx' limit 1", "type": "query", "params": [], "bindings": ["uwihuBM9XZ0NysnvoeBDaQizVKRVklkJACdwi3Kx"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.679899, "duration": 0.00894, "duration_str": "8.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database.sqlite", "explain": null, "start_percent": 0, "width_percent": 25.808}, {"sql": "select * from \"cache\" where \"key\" in ('<EMAIL>|127.0.0.1')", "type": "query", "params": [], "bindings": ["<EMAIL>|127.0.0.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 300}], "start": 1759800334.289898, "duration": 0.00617, "duration_str": "6.17ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "database.sqlite", "explain": null, "start_percent": 25.808, "width_percent": 17.812}, {"sql": "select * from \"users\" where \"email\" = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 414}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 411}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 333}, {"index": 22, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 44}], "start": **********.621234, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:138", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=138", "ajax": false, "filename": "EloquentUserProvider.php", "line": "138"}, "connection": "database.sqlite", "explain": null, "start_percent": 43.62, "width_percent": 2.483}, {"sql": "delete from \"sessions\" where \"id\" = 'uwihuBM9XZ0NysnvoeBDaQizVKRVklkJACdwi3Kx'", "type": "query", "params": [], "bindings": ["uwihuBM9XZ0NysnvoeBDaQizVKRVklkJACdwi3Kx"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 619}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 578}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 549}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 422}], "start": 1759800337.8172922, "duration": 0.01696, "duration_str": "16.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:268", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=268", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "268"}, "connection": "database.sqlite", "explain": null, "start_percent": 46.103, "width_percent": 48.961}, {"sql": "delete from \"cache\" where \"key\" in ('<EMAIL>|127.0.0.1', 'workwise_cache_illuminate:cache:flexible:created:<EMAIL>|127.0.0.1')", "type": "query", "params": [], "bindings": ["<EMAIL>|127.0.0.1", "workwise_cache_illuminate:cache:flexible:created:<EMAIL>|127.0.0.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 388}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 363}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 219}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 260}], "start": **********.0546792, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:388", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=388", "ajax": false, "filename": "DatabaseStore.php", "line": "388"}, "connection": "database.sqlite", "explain": null, "start_percent": 95.064, "width_percent": 2.887}, {"sql": "delete from \"cache\" where \"key\" in ('<EMAIL>|127.0.0.1:timer', 'workwise_cache_illuminate:cache:flexible:created:<EMAIL>|127.0.0.1:timer')", "type": "query", "params": [], "bindings": ["<EMAIL>|127.0.0.1:timer", "workwise_cache_illuminate:cache:flexible:created:<EMAIL>|127.0.0.1:timer"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 388}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 363}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 262}, {"index": 16, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\app\\Http\\Requests\\Auth\\LoginRequest.php", "line": 52}], "start": **********.135948, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:388", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\WorkWise\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=388", "ajax": false, "filename": "DatabaseStore.php", "line": "388"}, "connection": "database.sqlite", "explain": null, "start_percent": 97.95, "width_percent": 2.05}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/login", "action_name": null, "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "uri": "POST login", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store<a href=\"phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FToshiba%2FDesktop%2FWorkWise%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:30-44</a>", "middleware": "web, guest", "duration": "9.32s", "peak_memory": "6MB", "response": "Redirect to http://127.0.0.1:8000/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1802275746 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1802275746\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2092088287 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"27 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>remember</span>\" => <span class=sf-dump-const>false</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2092088287\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1312454389 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IllTQi9HVXFqQitla3UyMjIrYmtkWHc9PSIsInZhbHVlIjoiNnlhbnBDMlRNNFFESGJwWVoxODFFbGFXME5ySUMzdHJoQnpzUGF3MHhraHZtLzN2YndSbzh2SWlFQ2pkaG02YlZ4eGh2OE5JVERGbWdDZzdBcjJlQkdkODNVZmU2TmJtMXhFZXl2ZUNybnFnb21PMGZhd21Oek5ONllLOVdyekYiLCJtYWMiOiJmODY5ZDU0YjUxNDJlZDk4MjQyYTJiMGVjNTNhZWQwMjVlMDI4YTA0MmMwZDA0MTBhZWFiZDMyNGYxNjdjMzQxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IllTQi9HVXFqQitla3UyMjIrYmtkWHc9PSIsInZhbHVlIjoiNnlhbnBDMlRNNFFESGJwWVoxODFFbGFXME5ySUMzdHJoQnpzUGF3MHhraHZtLzN2YndSbzh2SWlFQ2pkaG02YlZ4eGh2OE5JVERGbWdDZzdBcjJlQkdkODNVZmU2TmJtMXhFZXl2ZUNybnFnb21PMGZhd21Oek5ONllLOVdyekYiLCJtYWMiOiJmODY5ZDU0YjUxNDJlZDk4MjQyYTJiMGVjNTNhZWQwMjVlMDI4YTA0MmMwZDA0MTBhZWFiZDMyNGYxNjdjMzQxIiwidGFnIjoiIn0%3D; workwise_session=eyJpdiI6ImhsRnpRcUxYNllyUndDSktFQ01ReXc9PSIsInZhbHVlIjoiNnphNTBWbkNUblNuSktQS2p5dkh3WG90M3dtdlFaOE1HYmJIemt0N1hTbTJGMk1qV3M2UjFpUk9Odi9rbDBDVWc1c0hZZmgzZExENE50UEdmcC9SOVoxcWN6cE9yYmV3UnphVUNIYnczTEIvWW5ZWnNyaGVoeENnaHRaRWFCcjAiLCJtYWMiOiIxYjcyNzVkNGIwMjFkYmM5YTlhYmJjOTU0NmM1YzFmZjUyYmU1ZWY4OGVkMTY1ZTYwNjA5ODdkZjYzMTUxNmExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312454389\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1938332548 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">loWtgLiL3uGHoVfUQ4MtCqvkccaZ6h5sf3DspbGf</span>\"\n  \"<span class=sf-dump-key>workwise_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uwihuBM9XZ0NysnvoeBDaQizVKRVklkJACdwi3Kx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938332548\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-685149323 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 07 Oct 2025 01:25:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685149323\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1090571865 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">V8CEQ6BjfyNFplbxjZlRZR18PnQaTPTwpQRrp0dO</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090571865\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store"}, "badge": "302 Found"}}