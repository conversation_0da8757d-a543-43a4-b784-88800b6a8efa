{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.2.1", "vite": "^6.2.4"}, "dependencies": {"@fontsource/dancing-script": "^5.2.6", "@heroicons/react": "^2.2.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "chart.js": "^4.4.0", "date-fns": "^4.1.0", "lucide-react": "^0.544.0", "recharts": "^2.15.3"}}