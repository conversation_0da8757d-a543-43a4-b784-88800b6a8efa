<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>WorkWise - Futuristic AI Design</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&amp;family=Roboto:wght@400;500;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style type="text/tailwindcss">
    @layer utilities {
      @keyframes float {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-8px);
        }
      }
      .animate-float {
        animation: float 6s ease-in-out infinite;
      }
      @keyframes node-pulse {
        0%,
        100% {
          transform: scale(1);
          opacity: 0.8;
        }
        50% {
          transform: scale(1.1);
          opacity: 1;
        }
      }
      @keyframes gentle-float {
        0%,
        100% {
          transform: translateY(0) translateX(0) rotate(0deg);
        }
        25% {
          transform: translateY(-15px) translateX(8px) rotate(3deg);
        }
        50% {
          transform: translateY(0px) translateX(-12px) rotate(-2deg);
        }
        75% {
          transform: translateY(12px) translateX(4px) rotate(1.5deg);
        }
      }
      .animate-gentle-float {
        animation: gentle-float 20s ease-in-out infinite;
      }
      @keyframes holographic-glow {
        0%,
        100% {
          text-shadow: 0 0 4px rgba(59, 130, 246, 0.4), 0 0 8px rgba(59, 130, 246, 0.3), 0 0 12px rgba(59, 130, 246, 0.2);
        }
        50% {
          text-shadow: 0 0 8px rgba(59, 130, 246, 0.6), 0 0 16px rgba(59, 130, 246, 0.4), 0 0 24px rgba(59, 130, 246, 0.3);
        }
      }
      .holographic-text {
        animation: holographic-glow 4s ease-in-out infinite;
      }
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .fade-in {
        animation: fadeIn 0.8s ease-out forwards;
      }
      @keyframes reveal {
        from {
          clip-path: inset(0 100% 0 0);
        }
        to {
          clip-path: inset(0 0 0 0);
        }
      }
      .animate-reveal {
        animation: reveal 0.8s ease-in-out forwards;
      }
    }
  </style>
<script>
    tailwind.config = {
      darkMode: "class",
      theme: {
        extend: {
          colors: {
            primary: "#3b82f6",
            "background-light": "#0a0a0a",
            "background-dark": "#0a0a0a",
            "card-light": "rgba(31, 31, 33, 0.5)",
            "card-dark": "rgba(31, 31, 33, 0.5)",
            "text-light": "#f0f0f0",
            "text-dark": "#f0f0f0",
            "text-secondary-light": "#a0a0a0",
            "text-secondary-dark": "#a0a0a0",
          },
          fontFamily: {
            display: ["Orbitron", "sans-serif"],
            body: ["Roboto", "sans-serif"],
          },
          borderRadius: {
            DEFAULT: "0.5rem",
            lg: "1rem",
            xl: "1.5rem"
          },
          boxShadow: {
            'glow': '0 0 15px rgba(59, 130, 246, 0.4), 0 0 30px rgba(59, 130, 246, 0.2)',
            'glow-hover': '0 0 25px rgba(59, 130, 246, 0.6), 0 0 40px rgba(59, 130, 246, 0.4)',
            'holographic-glow': '0 0 8px rgba(59, 130, 246, 0.5), 0 0 16px rgba(59, 130, 246, 0.3), 0 0 24px rgba(59, 130, 246, 0.1)',
          },
          animation: {
            'gradient-x': 'gradient-x 8s ease infinite',
          },
          keyframes: {
            'gradient-x': {
              '0%, 100%': {
                'background-size': '200% 200%',
                'background-position': 'left center'
              },
              '50%': {
                'background-size': '200% 200%',
                'background-position': 'right center'
              }
            },
          }
        },
      },
    };
  </script>
<style>
    body {
      background-color: #0a0a0a;
      color: #f0f0f0;
      font-family: 'Roboto', sans-serif;
      overflow-x: hidden;
    }
    .glow-border {
      border: 1px solid transparent;
      position: relative;
      background-clip: padding-box;
      backdrop-filter: blur(10px);
      transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1), box-shadow 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    }
    .glow-border::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: -1;
      margin: -1px;
      border-radius: inherit;
      background: linear-gradient(120deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.2));
      transition: all 0.4s ease-in-out;
    }
    .glow-border:hover {
      transform: translateY(-8px) scale(1.03);
      box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
    }
    .glow-border:hover::before {
      background: linear-gradient(120deg, rgba(59, 130, 246, 0.6), rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.6));
      filter: blur(8px) brightness(1.3);
    }
    .neural-network-bg {
      position: fixed;
      inset: 0;
      overflow: hidden;
      z-index: -1;
      opacity: 0.15;
    }
    .floating-elements {
      position: fixed;
      inset: 0;
      z-index: -1;
      overflow: hidden;
      pointer-events: none;
    }
    .floating-element {
      position: absolute;
      opacity: 0.08;
      filter: blur(2px);
      color: #3b82f6;
    }
    #particle-canvas {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10;
      pointer-events: none;
      transition: opacity 0.5s ease-out;
    }
    [data-observer-target] {
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.8s ease-out, transform 0.8s ease-out;
    }
    [data-observer-target].is-visible {
      opacity: 1;
      transform: translateY(0);
    }
  </style>
</head>
<body class="bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark">
<canvas id="particle-canvas"></canvas>
<div class="relative min-h-screen">
<div class="floating-elements">
<div class="floating-element text-5xl animate-gentle-float" style="top: 15%; left: 10%; animation-duration: 28s;">△</div>
<div class="floating-element text-6xl animate-gentle-float" style="top: 70%; left: 85%; animation-duration: 35s; animation-delay: 5s;">▣</div>
<div class="floating-element text-4xl animate-gentle-float" style="top: 80%; left: 5%; animation-duration: 22s;">●</div>
<div class="floating-element text-7xl animate-gentle-float" style="top: 5%; left: 80%; animation-duration: 40s; animation-delay: 2s;">
<svg fill="none" height="80" stroke="currentColor" stroke-width="1.5" viewBox="0 0 100 100" width="80" xmlns="http://www.w3.org/2000/svg">
<path d="M20 80 L50 20 L80 80 Z" stroke-dasharray="8 4" stroke-linecap="round"></path>
</svg>
</div>
<div class="absolute w-1 h-1 bg-primary rounded-full shadow-[0_0_10px_#3b82f6] animate-gentle-float" style="top: 25%; left: 30%; animation-duration: 20s;"></div>
<div class="absolute w-2 h-2 bg-blue-400 rounded-full shadow-[0_0_15px_#60a5fa] animate-gentle-float" style="top: 50%; left: 50%; animation-duration: 26s; animation-delay: 3s;"></div>
<div class="absolute w-1 h-1 bg-primary rounded-full shadow-[0_0_8px_#3b82f6] animate-gentle-float" style="top: 90%; left: 20%; animation-duration: 32s; animation-delay: 1s;"></div>
<div class="absolute w-1.5 h-1.5 bg-blue-300 rounded-full shadow-[0_0_12px_#93c5fd] animate-gentle-float" style="top: 10%; left: 60%; animation-duration: 21s; animation-delay: 4s;"></div>
<div class="floating-element text-sm font-mono animate-gentle-float" style="top: 40%; left: 90%; animation-duration: 30s;">[01101110]</div>
<div class="floating-element text-xs font-mono animate-gentle-float" style="top: 60%; left: 15%; animation-duration: 38s; animation-delay: 6s;">&lt;/&gt;</div>
<div class="floating-element text-base font-mono animate-gentle-float" style="top: 5%; left: 25%; animation-duration: 27s; animation-delay: 3s;">{...}</div>
</div>
<div class="neural-network-bg">
<canvas id="neural-network-canvas"></canvas>
</div>
<div class="absolute inset-0 z-0 opacity-20">
<div class="absolute top-[-150px] left-[-150px] w-[500px] h-[500px] bg-primary rounded-full filter blur-3xl opacity-25 animate-pulse" style="animation-duration: 8s;"></div>
<div class="absolute bottom-[-150px] right-[-150px] w-[500px] h-[500px] bg-blue-700 rounded-full filter blur-3xl opacity-25 animate-pulse" style="animation-duration: 8s; animation-delay: 4s;"></div>
</div>
<div class="relative z-20 container mx-auto px-4 sm:px-6 lg:px-8">
<header class="py-6 flex justify-between items-center opacity-0 fade-in" style="animation-delay: 0.1s;">
<h1 class="text-3xl font-display font-bold text-white transition-all duration-300 hover:text-primary hover:drop-shadow-[0_0_10px_rgba(59,130,246,0.8)] holographic-text">WorkWise</h1>
<nav class="flex items-center space-x-6">
<a class="text-text-secondary-light dark:text-text-secondary-dark hover:text-white transition-colors duration-300 relative group" href="#">
            Log In
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
</a>
<a class="bg-primary text-white font-bold py-2 px-6 rounded-lg hover:bg-blue-500 transition-all duration-300 shadow-glow hover:shadow-glow-hover transform hover:scale-105" href="#">Get Started</a>
</nav>
</header>
<main class="text-center pt-24 pb-32">
<h2 class="font-display text-5xl md:text-7xl font-bold mb-6 tracking-wider bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-300 animate-gradient-x opacity-0 fade-in" style="animation-delay: 0.3s;">
          Connect. Create. <span class="text-primary drop-shadow-[0_0_15px_rgba(59,130,246,0.6)] holographic-text animate-reveal" style="animation-delay: 0.7s;">Collaborate.</span>
</h2>
<p class="max-w-3xl mx-auto text-lg text-text-secondary-light dark:text-text-secondary-dark mb-10 opacity-0 fade-in" style="animation-delay: 0.5s;">
          WorkWise is an AI-driven marketplace that connects talented gig workers with innovative companies. Find your next project or discover the perfect talent for your needs.
        </p>
<div class="flex justify-center items-center space-x-4 opacity-0 fade-in" style="animation-delay: 0.7s;">
<a class="bg-primary text-white font-bold py-3 px-8 rounded-lg hover:bg-blue-500 transition-all duration-300 shadow-glow hover:shadow-glow-hover text-lg transform hover:-translate-y-1" href="#">Browse Jobs</a>
<a class="bg-card-dark border border-primary/50 text-primary font-bold py-3 px-8 rounded-lg hover:bg-primary hover:text-white transition-all duration-300 text-lg relative overflow-hidden group shadow-holographic-glow" href="#">
<span class="absolute w-0 h-full bg-primary/30 left-0 top-0 transition-all duration-500 ease-out group-hover:w-full"></span>
<span class="relative">Join WorkWise</span>
</a>
</div>
</main>
<section class="grid grid-cols-1 md:grid-cols-3 gap-8 pb-24" id="features-section">
<div class="bg-card-light dark:bg-card-dark p-8 rounded-xl text-center glow-border flex flex-col items-center" data-observer-target="">
<div class="bg-primary/20 p-4 rounded-full mb-6 inline-block shadow-glow animate-float">
<span class="material-icons text-primary text-4xl holographic-text">auto_awesome</span>
</div>
<h3 class="text-2xl font-display font-bold mb-3 text-white holographic-text">Smart Matching</h3>
<p class="text-text-secondary-light dark:text-text-secondary-dark">
            Our AI-powered system matches gig workers with projects based on skills, experience, and preferences for perfect collaborations.
          </p>
</div>
<div class="bg-card-light dark:bg-card-dark p-8 rounded-xl text-center glow-border flex flex-col items-center" data-observer-target="" style="transition-delay: 0.15s;">
<div class="bg-primary/20 p-4 rounded-full mb-6 inline-block shadow-glow animate-float" style="animation-delay: 0.5s;">
<span class="material-icons text-primary text-4xl holographic-text">verified_user</span>
</div>
<h3 class="text-2xl font-display font-bold mb-3 text-white holographic-text">Secure Payments</h3>
<p class="text-text-secondary-light dark:text-text-secondary-dark">
            Built-in escrow system ensures secure transactions and timely payments for both gig workers and employers.
          </p>
</div>
<div class="bg-card-light dark:bg-card-dark p-8 rounded-xl text-center glow-border flex flex-col items-center" data-observer-target="" style="transition-delay: 0.3s;">
<div class="bg-primary/20 p-4 rounded-full mb-6 inline-block shadow-glow animate-float" style="animation-delay: 1s;">
<span class="material-icons text-primary text-4xl holographic-text">workspace_premium</span>
</div>
<h3 class="text-2xl font-display font-bold mb-3 text-white holographic-text">Quality Talent</h3>
<p class="text-text-secondary-light dark:text-text-secondary-dark">
            Access to a curated network of skilled professionals across various industries and expertise levels.
          </p>
</div>
</section>
</div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', () => {
      const nnCanvas = document.getElementById('neural-network-canvas');
      const nnCtx = nnCanvas.getContext('2d');
      let nnWidth = nnCanvas.width = window.innerWidth;
      let nnHeight = nnCanvas.height = document.documentElement.scrollHeight;
      const particleCanvas = document.getElementById('particle-canvas');
      const particleCtx = particleCanvas.getContext('2d');
      particleCanvas.width = window.innerWidth;
      particleCanvas.height = window.innerHeight;
      let mouse = {
        x: null,
        y: null,
        radius: 150
      }
      window.addEventListener('mousemove', (event) => {
        mouse.x = event.x;
        mouse.y = event.y;
      });
      window.addEventListener('mouseout', () => {
        mouse.x = null;
        mouse.y = null;
      });
      window.addEventListener('resize', () => {
        nnWidth = nnCanvas.width = window.innerWidth;
        nnHeight = nnCanvas.height = document.documentElement.scrollHeight;
        particleCanvas.width = window.innerWidth;
        particleCanvas.height = window.innerHeight;
        initNeuralNetwork();
        initParticles();
      });
      const nodeColor = 'rgba(59, 130, 246, 0.7)';
      const lineColor = 'rgba(59, 130, 246, 0.15)';
      let nodes = [];
      class Node {
        constructor(x, y, radius) {
          this.x = x;
          this.y = y;
          this.radius = radius;
          this.vx = (Math.random() - 0.5) * 0.2;
          this.vy = (Math.random() - 0.5) * 0.2;
        }
        draw() {
          nnCtx.beginPath();
          nnCtx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
          nnCtx.fillStyle = nodeColor;
          nnCtx.fill();
        }
        update() {
          this.x += this.vx;
          this.y += this.vy;
          if (this.x < 0 || this.x > nnWidth) this.vx *= -1;
          if (this.y < 0 || this.y > nnHeight) this.vy *= -1;
        }
      }
      function initNeuralNetwork() {
        nodes = [];
        const nodeCount = Math.floor((nnWidth * nnHeight) / 30000);
        for (let i = 0; i < nodeCount; i++) {
          nodes.push(new Node(Math.random() * nnWidth, Math.random() * nnHeight, Math.random() * 1.5 + 1));
        }
      }
      function connectNodes() {
        let maxDistance = 180;
        for (let i = 0; i < nodes.length; i++) {
          for (let j = i + 1; j < nodes.length; j++) {
            const dx = nodes[i].x - nodes[j].x;
            const dy = nodes[i].y - nodes[j].y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance < maxDistance) {
              nnCtx.beginPath();
              nnCtx.moveTo(nodes[i].x, nodes[i].y);
              nnCtx.lineTo(nodes[j].x, nodes[j].y);
              nnCtx.strokeStyle = lineColor;
              nnCtx.lineWidth = 0.8 - (distance / maxDistance);
              nnCtx.stroke();
            }
          }
        }
      }
      let particlesArray = [];
      class Particle {
        constructor(x, y, size, color, weight) {
          this.x = x;
          this.y = y;
          this.size = size;
          this.color = color;
          this.weight = weight;
          this.baseX = this.x;
          this.baseY = this.y;
        }
        draw() {
          particleCtx.beginPath();
          particleCtx.arc(this.x, this.y, this.size, 0, Math.PI * 2, false);
          particleCtx.fillStyle = this.color;
          particleCtx.fill();
        }
        update() {
          let dx = mouse.x - this.x;
          let dy = mouse.y - this.y;
          let distance = Math.sqrt(dx * dx + dy * dy);
          let forceDirectionX = dx / distance;
          let forceDirectionY = dy / distance;
          let maxDistance = mouse.radius;
          let force = (maxDistance - distance) / maxDistance;
          let directionX = forceDirectionX * force * this.weight;
          let directionY = forceDirectionY * force * this.weight;
          if (distance < mouse.radius) {
            this.x -= directionX * 2.5;
            this.y -= directionY * 2.5;
          } else {
            if (this.x !== this.baseX) {
              let dx = this.x - this.baseX;
              this.x -= dx / 15;
            }
            if (this.y !== this.baseY) {
              let dy = this.y - this.baseY;
              this.y -= dy / 15;
            }
          }
        }
      }
      function initParticles() {
        particlesArray = [];
        const interactiveElements = document.querySelectorAll('a[href="#"], button');
        interactiveElements.forEach(el => {
          const rect = el.getBoundingClientRect();
          for (let i = 0; i < 15; i++) {
            let size = (Math.random() * 1.5) + 0.5;
            let x = rect.left + Math.random() * rect.width;
            let y = rect.top + Math.random() * rect.height;
            let color = `rgba(59, 130, 246, ${Math.random() * 0.4 + 0.2})`;
            let weight = Math.random() * 1.5 + 0.5;
            particlesArray.push(new Particle(x, y, size, color, weight));
          }
        });
      }
      function animate() {
        nnCtx.clearRect(0, 0, nnWidth, nnHeight);
        nodes.forEach(node => {
          node.update();
          node.draw();
        });
        connectNodes();
        particleCtx.clearRect(0, 0, particleCanvas.width, particleCanvas.height);
        for (let i = 0; i < particlesArray.length; i++) {
          particlesArray[i].update();
          particlesArray[i].draw();
        }
        requestAnimationFrame(animate);
      }
      initNeuralNetwork();
      initParticles();
      animate();
      const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('is-visible');
            observer.unobserve(entry.target);
          }
        });
      }, {
        threshold: 0.1
      });
      document.querySelectorAll('[data-observer-target]').forEach(el => {
        observer.observe(el);
      });
    });
  </script>

</body></html>